#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
下载官方ONNX OCR模型脚本
基于OpenCV官方文档推荐的模型
"""

import os
import urllib.request
import hashlib
from pathlib import Path

def download_file(url, filename, expected_sha=None):
    """下载文件并验证SHA"""
    print(f"正在下载: {filename}")
    print(f"URL: {url}")
    
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"下载完成: {filename}")
        
        if expected_sha:
            # 验证文件SHA
            with open(filename, 'rb') as f:
                file_hash = hashlib.sha1(f.read()).hexdigest()
            
            if file_hash == expected_sha:
                print(f"SHA验证通过: {file_hash}")
            else:
                print(f"SHA验证失败: 期望 {expected_sha}, 实际 {file_hash}")
                return False
        
        return True
    except Exception as e:
        print(f"下载失败: {e}")
        return False

def main():
    # 创建models目录
    models_dir = Path("models")
    models_dir.mkdir(exist_ok=True)
    
    # 官方推荐的CRNN文本识别模型 (固定形状)
    models = [
        {
            "name": "crnn.onnx",
            "url": "https://drive.google.com/uc?export=download&id=1ooaLR-rkTl8jdpGy1DoQs0-X0lQsB6Fj",
            "sha": "270d92c9ccb670ada2459a25977e8deeaf8380d3",
            "description": "CRNN文本识别模型 (36字符: 0-9 + a-z)"
        },
        {
            "name": "alphabet_36.txt",
            "url": "https://drive.google.com/uc?export=download&id=1oPOYx5rQRp8L6XQciUwmwhMCfX0KyO4b",
            "sha": None,
            "description": "36字符字典文件"
        },
        {
            "name": "crnn_cs.onnx",
            "url": "https://drive.google.com/uc?export=download&id=12diBsVJrS9ZEl6BNUiRp9s0xPALBS7kt",
            "sha": "a641e9c57a5147546f7a2dbea4fd322b47197cd5",
            "description": "CRNN文本识别模型 (94字符: 0-9 + a-z + A-Z + 标点)"
        },
        {
            "name": "alphabet_94.txt",
            "url": "https://drive.google.com/uc?export=download&id=1oKXxXKusquimp7XY1mFvj9nwLzldVgBR",
            "sha": None,
            "description": "94字符字典文件"
        },
        {
            "name": "DB_IC15_resnet50.onnx",
            "url": "https://drive.google.com/uc?export=download&id=17_ABp79PlFt9yPCxSaarVc_DKTmrSGGf",
            "sha": "bef233c28947ef6ec8c663d20a2b326302421fa3",
            "description": "DB文本检测模型 (英文)"
        }
    ]
    
    print("开始下载官方ONNX OCR模型...")
    print("="*50)
    
    success_count = 0
    for model in models:
        print(f"\n处理: {model['description']}")
        filepath = models_dir / model["name"]
        
        if filepath.exists():
            print(f"文件已存在: {filepath}")
            success_count += 1
            continue
        
        if download_file(model["url"], str(filepath), model["sha"]):
            success_count += 1
        else:
            print(f"下载失败: {model['name']}")
    
    print("\n" + "="*50)
    print(f"下载完成: {success_count}/{len(models)} 个文件")
    
    if success_count == len(models):
        print("\n所有模型下载成功!")
        print("\n模型信息:")
        print("- crnn.onnx: 输入形状 [1, 1, 32, 100], 输出CTC概率")
        print("- crnn_cs.onnx: 输入形状 [1, 3, 32, 100], 输出CTC概率")
        print("- DB_IC15_resnet50.onnx: 文本检测模型")
        print("\n使用说明:")
        print("1. crnn.onnx 使用灰度图像 (1通道)")
        print("2. crnn_cs.onnx 使用彩色图像 (3通道)")
        print("3. 输入图像尺寸固定为 32x100")
        print("4. 使用CTC贪婪解码")
    else:
        print("\n部分模型下载失败，请检查网络连接")

if __name__ == "__main__":
    main()