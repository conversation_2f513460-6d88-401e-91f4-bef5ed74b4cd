<!doctype html><html lang="zh-cmn-<PERSON>-C<PERSON>"><head><meta charset="utf-8"/><!--[if IE]><link rel="shortcut icon" href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/favicon.ico" /><![endif]--><link rel="icon" href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/favicon-48.png" sizes="48x48"/><link rel="icon" href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/favicon-64.png" sizes="64x64"/><link rel="icon" href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/favicon-128.png" sizes="128x128"/><meta name="viewport" content="width=device-width,initial-scale=1"/><meta name="theme-color" content="#fff"/><link rel="manifest" href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/manifest.json"/><title>下载安装 Linux 推理库-PaddlePaddle深度学习平台</title><meta name="keywords" content="下载安装 Linux 推理库,下载安装 Windows 推理库,下载安装 Mac 推理库C++ 推理库,Python 推理库,C++ 推理库,C++ 推理库开源深度学习平台,PaddlePaddle,飞桨,PaddlePaddle教程"/><meta name="description" content="预编译包使用方式见：推理示例（C++） 预编译包使用方式见：推理示例（Python） 预编译包使用方式见：推理示例（C++） 预编译包使用方式见：推理示例（C++） © Copyright 2020,"/><script>if (typeof (_historyPolyfill) === 'undefined') {

        var _historyPolyfill = function (type) {
            var orig = history[type];

            function createNewEvent(eventName) {
                var event;
                if (typeof(Event) === 'function') {
                    event = new Event(eventName);
                } else {
                    event = document.createEvent('Event');
                    event.initEvent(eventName, true, true);
                }
                return event;
            }

            return function () {
                var rv = orig.apply(this, arguments);
                var typelow = type.toLowerCase();
                var ontype = 'on' + typelow;
                var e = createNewEvent(typelow);
                e.arguments = arguments;
                window.dispatchEvent(e);
                typeof (window[ontype]) === 'function' && window[ontype].apply(this, arguments);
                return rv;
            };
        };
        history.pushState = _historyPolyfill('pushState');
        history.replaceState = _historyPolyfill('replaceState');
    }</script><script>// 百度统计
        var _hmt = _hmt || {};
        (function () {
            var baiduTj = function () {
                _hmt = _hmt || {};
                var hmid = 'baidu-tj';
                var oldHm = document.getElementById(hmid);
                if (oldHm) {
                    oldHm.parentNode.removeChild(oldHm);
                }
                var hm = document.createElement('script');
                hm.id = hmid;
                hm.src = 'https://hm.baidu.com/hm.js?89be97848720f62fa00a07b1e0d83ae6';
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(hm, s);
                setTimeout(function () {
                    if (typeof (_hmt.push) !== 'function') {
                        _hmt = undefined;
                    }
                }, 0);
            }
            baiduTj();
            window.addEventListener('popstate', baiduTj);
            window.addEventListener('pushstate', baiduTj);
            window.addEventListener('replacestate', baiduTj);
        })();</script><script>// 百度收录
        (function () {
            var baiduPush = function () {
                var bpid = 'baidu-push';
                var oldBp = document.getElementById('baidu-push');
                if (oldBp) {
                    oldBp.parentNode.removeChild(oldBp);
                }
                var bp = document.createElement('script');
                bp.id = bpid;
                var curProtocol = window.location.protocol.split(':')[0];
                if (curProtocol === 'https') {
                    bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
                }
                else {
                    bp.src = 'http://push.zhanzhang.baidu.com/push.js';
                }
                var s = document.getElementsByTagName('script')[0];
                s.parentNode.insertBefore(bp, s);
            }
            baiduPush();
            window.addEventListener('popstate', baiduPush);
            window.addEventListener('pushstate', baiduPush);
            window.addEventListener('replacestate', baiduPush);
        })();</script><style>.paddle-s-e-o {
        position: absolute;
        overflow: hidden;
        height: 0;
        width: 0;
    }</style><script defer="defer" src="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/static/js/verdor.19b9cc3d.js"></script><script defer="defer" src="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/static/js/common.627c84b2.js"></script><script defer="defer" src="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/static/js/inference.30237507.js"></script><link href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/static/css/verdor.cbeaa622.css" rel="stylesheet"><link href="https://paddlepaddle-org-cn.cdn.bcebos.com/paddle-unique-front/static/css/common.dab2e4f6.css" rel="stylesheet"></head><body class="wy-body-for-nav"><style>.paddle-logo {
        position: absolute;
        overflow: hidden;
        height: 0;
        width: 0;
    }</style><div class="paddle-logo"><img src=""/></div><noscript>You need to enable JavaScript to run this app.</noscript><div style="height: 0; overflow: hidden">\u200E</div><div id="root"></div><script>(function(){window.pageData={"navData":[{"title":"开始使用","href":"/start","items":[]},{"title":"特性","href":"/feature","items":[]},{"title":"文档","href":"","items":[{"title":"API","href":"/documentation/docs/zh/1.5/api_cn/index_cn.html?from=paddlenav","items":[]},{"title":"使用指南","href":"/documentation/docs/zh/1.5/user_guides/index_cn.html?from=paddlenav","items":[]}]},{"title":"工具平台","href":"","items":[{"title":"工具","href":"","items":[{"title":"AutoDL","href":"https://github.com/PaddlePaddle/AutoDL/tree/master/AutoDL%20Design"},{"title":"PaddleHub","href":"/hub"},{"title":"PARL","href":"https://github.com/paddlepaddle/parl"},{"title":"ERNIE","href":"/ernie"},{"title":"全部","href":"/tools"}]},{"title":"平台","href":"","items":[{"title":"AI Studio","href":"https://aistudio.baidu.com"},{"title":"EasyDL","href":"https://ai.baidu.com/easydl/"},{"title":"EasyEdge","href":"https://ai.baidu.com/easyedge/home"}]}]},{"title":"资源","href":"","items":[{"title":"模型和数据集","href":"/modelanddataset","items":[]},{"title":"学习资料","href":"/learningmaterials","items":[]},{"title":"应用案例","href":"/case","items":[]}]}],"seo":{"title":"下载安装 Linux 推理库-PaddlePaddle深度学习平台","keywords":"下载安装 Linux 推理库,下载安装 Windows 推理库,下载安装 Mac 推理库C++ 推理库,Python 推理库,C++ 推理库,C++ 推理库开源深度学习平台,PaddlePaddle,飞桨,PaddlePaddle教程","description":"预编译包使用方式见：推理示例（C++） 预编译包使用方式见：推理示例（Python） 预编译包使用方式见：推理示例（C++） 预编译包使用方式见：推理示例（C++） © Copyright 2020,"},"pageData":null}})();</script><script>window.docInfo={};</script><script>(function(){window.docInfo.lang="zh";})();</script><script>(function(){window.docInfo.version="master";})();</script><div class="paddle-s-e-o"><div class="header"><ul><li><a href="/start">开始使用</a></li><li><a href="/feature">特性</a></li><li>文档<ul><li><a href="/documentation/docs/zh/1.5/api_cn/index_cn.html?from=paddlenav">API</a></li><li><a href="/documentation/docs/zh/1.5/user_guides/index_cn.html?from=paddlenav">使用指南</a></li></ul></li><li>工具平台<ul><li>工具<ul><li><a href="https://github.com/PaddlePaddle/AutoDL/tree/master/AutoDL%20Design">AutoDL</a></li><li><a href="/hub">PaddleHub</a></li><li><a href="https://github.com/paddlepaddle/parl">PARL</a></li><li><a href="/ernie">ERNIE</a></li><li><a href="/tools">全部</a></li></ul></li><li>平台<ul><li><a href="https://aistudio.baidu.com">AI Studio</a></li><li><a href="https://ai.baidu.com/easydl/">EasyDL</a></li><li><a href="https://ai.baidu.com/easyedge/home">EasyEdge</a></li></ul></li></ul></li><li>资源<ul><li><a href="/modelanddataset">模型和数据集</a></li><li><a href="/learningmaterials">学习资料</a></li><li><a href="/case">应用案例</a></li></ul></li></ul></div></div><div class="paddle-s-e-o"><div class="docheader"><ul id="doc_version" class="doc-version"><li><a href="/change-version?version=v3.0">v3.0</a></li><li class="current"><a href="/change-version?version=master">master</a></li><li><a href="/change-version?version=v2.6">v2.6</a></li><li><a href="/change-version?version=v2.5">v2.5</a></li><li><a href="/change-version?version=v2.4">v2.4</a></li><li><a href="/change-version?version=v2.3">v2.3</a></li><li><a href="/change-version?version=v2.2">v2.2</a></li><li><a href="/change-version?version=v2.1">v2.1</a></li><li><a href="/change-version?version=v2.0">v2.0</a></li><li><a href="/change-version?version=v1.8">v1.8</a></li></ul><ul id="doc_lang" class="doc-lang"><li class="current"><a href="/change-lang?lang=zh">中文(简)</a></li><li><a href="/change-lang?lang=en">English(En)</a></li></ul></div><div id="document_content"><!DOCTYPE html>
<html class="writer-html5" lang="en" >
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>下载安装 Linux 推理库 &mdash; Paddle-Inference  documentation</title>
      <link rel="stylesheet" href="../../_static/pygments.css" type="text/css" />
      <link rel="stylesheet" href="../../_static/css/theme.css" type="text/css" />
  <!--[if lt IE 9]>
    <script src="../../_static/js/html5shiv.min.js"></script>
  <![endif]-->
  
        <script data-url_root="../../" id="documentation_options" src="../../_static/documentation_options.js"></script>
        <script src="../../_static/jquery.js"></script>
        <script src="../../_static/underscore.js"></script>
        <script src="../../_static/doctools.js"></script>
    <script src="../../_static/js/theme.js"></script>
    <link rel="index" title="Index" href="../../genindex.html" />
    <link rel="search" title="Search" href="../../search.html" />
    <link rel="next" title="源码编译" href="compile/index_compile.html" />
    <link rel="prev" title="安装 C++ API" href="cpp_install.html" /> 
</head>

<body class="wy-body-for-nav"> 
  <div class="wy-grid-for-nav">
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-scroll">
        <div class="wy-side-nav-search" >
            <a href="../../index.html" class="icon icon-home"> Paddle-Inference
          </a>
              <div class="version">
                latest
              </div>
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
        </div><div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="Navigation menu">
              <ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="../index_guides.html">使用指南</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="../introduction/index_intro.html">Paddle Inference 简介</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../introduction/summary.html">如何选择正确的推理引擎</a></li>
<li class="toctree-l3"><a class="reference internal" href="../introduction/workflow.html">推理流程</a></li>
<li class="toctree-l3"><a class="reference internal" href="../introduction/design.html">架构设计</a></li>
</ul>
</li>
<li class="toctree-l2 current"><a class="reference internal" href="index_install.html">安装指南</a><ul class="current">
<li class="toctree-l3"><a class="reference internal" href="requirements.html">系统要求</a></li>
<li class="toctree-l3"><a class="reference internal" href="python_install.html">安装 Python API</a></li>
<li class="toctree-l3"><a class="reference internal" href="cpp_install.html">安装 C++ API</a></li>
<li class="toctree-l3 current"><a class="current reference internal" href="#">下载安装 Linux 推理库</a></li>
<li class="toctree-l3"><a class="reference internal" href="#windows">下载安装 Windows 推理库</a></li>
<li class="toctree-l3"><a class="reference internal" href="#mac">下载安装 Mac 推理库</a></li>
<li class="toctree-l3"><a class="reference internal" href="compile/index_compile.html">源码编译</a><ul>
<li class="toctree-l4"><a class="reference internal" href="compile/compile_basic.html">源码编译基础</a></li>
<li class="toctree-l4"><a class="reference internal" href="compile/source_compile_under_Linux.html">Linux 下从源码编译</a></li>
<li class="toctree-l4"><a class="reference internal" href="compile/source_compile_under_Windows.html">Windows 下从源码编译</a></li>
<li class="toctree-l4"><a class="reference internal" href="compile/source_compile_under_MacOS.html">macOS 下从源码编译</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../quick_start/index_quick_start.html">快速开始</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../quick_start/python_demo.html">快速上手Python推理</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick_start/cpp_demo.html">快速上手C++推理</a></li>
<li class="toctree-l3"><a class="reference internal" href="../quick_start/jit_inference.html">Paddle Inference 支持动态图 &amp; 静态图混合推理装饰器使用方法</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../export_model/index_export_model.html">导出模型</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../export_model/paddle_model_export.html">飞桨框架模型导出</a></li>
<li class="toctree-l3"><a class="reference internal" href="../export_model/outside_model_export.html">其他框架模型导出</a></li>
<li class="toctree-l3"><a class="reference internal" href="../export_model/visual_model.html">模型结构可视化</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../x86_cpu_infer/index_x86_cpu_infer.html">x86 CPU部署</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../x86_cpu_infer/paddle_x86_cpu.html">在x86 CPU上开发推理应用<!-- omit in toc --></a></li>
<li class="toctree-l3"><a class="reference internal" href="../x86_cpu_infer/paddle_x86_cpu_bf16.html">在x86 CPU上部署BF16模型</a></li>
<li class="toctree-l3"><a class="reference internal" href="../x86_cpu_infer/paddle_x86_cpu_int8.html">X86 CPU 上部署量化模型</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../nv_gpu_infer/index_nv_gpu_infer.html">NVIDIA-GPU部署</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../nv_gpu_infer/gpu_native_infer.html">GPU 原生推理</a></li>
<li class="toctree-l3"><a class="reference internal" href="../nv_gpu_infer/gpu_multi_stream.html">Paddle Inference GPU 多流推理</a></li>
<li class="toctree-l3"><a class="reference internal" href="../nv_gpu_infer/gpu_mixed_precision.html">混合精度推理</a></li>
<li class="toctree-l3"><a class="reference internal" href="../nv_gpu_infer/gpu_trt_infer.html">GPU TensorRT 加速推理(NV-GPU/Jetson)</a></li>
<li class="toctree-l3"><a class="reference internal" href="../nv_gpu_infer/trt_fp16_int8.html">GPU TensorRT 低精度或量化推理</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../hardware_support/index_hardware_support.html">其他硬件部署</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/cpu_phytium_cn.html">飞腾/鲲鹏 CPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/cpu_sunway_cn.html">申威 CPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/cpu_zhaoxin_cn.html">兆芯 CPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/cpu_loongson_cn.html">龙芯 CPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/xpu_kunlun_cn.html">昆仑 XPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/dcu_hygon_cn.html">AMD GPU / 海光 DCU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/npu_ascend_cn.html">昇腾 NPU 安装说明</a></li>
<li class="toctree-l3"><a class="reference internal" href="../hardware_support/ipu_graphcore_cn.html">Graphcore IPU 安装说明</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../demo_introduction.html">Paddle Inference 部署示例</a></li>
<li class="toctree-l2"><a class="reference internal" href="../performance_tuning/index_performance_tuning.html">调试与优化</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../performance_tuning/precision_tracing.html">精度核验与问题追查</a></li>
<li class="toctree-l3"><a class="reference internal" href="../performance_tuning/mixed_precision_diff_troubleshooting.html">混合精度推理diff原因排查方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../performance_tuning/performance_analysis_profiler.html">性能分析</a></li>
<li class="toctree-l3"><a class="reference internal" href="../performance_tuning/multi_thread.html">多线程并发推理</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../benchmark/index_benchmark.html">Benchmark</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../benchmark/benchmark_cpu.html">CPU 性能数据</a></li>
<li class="toctree-l3"><a class="reference internal" href="../benchmark/benchmark_gpu.html">GPU 性能数据</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../api_reference/index_api.html">API</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../api_reference/python_api_doc/python_api_index.html">Python API 文档</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/create_predictor.html">create_predictor 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/create_predictor.html#get-version">get_version 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/create_predictor.html#convert-to-mixed-precision">convert_to_mixed_precision 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/Config_index.html">Config 类</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/ConfigClass.html">1. Config 类定义</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/ModelConfig.html">2. 设置预测模型</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/CPUConfig.html">3. 使用 CPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/GPUConfig.html">4. 使用 GPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/XPUConfig.html">5. 使用 XPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/IPUConfig.html">6. 使用 IPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/ORTConfig.html">7. 使用 ONNXRuntime 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OptimConfig.html">8. 设置模型优化方法</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OtherFunction.html">9. 启用内存优化</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OtherFunction.html#shezhihuancunlujing">10. 设置缓存路径</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OtherFunction.html#profile">11. Profile 设置</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OtherFunction.html#log">12. Log 设置</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/python_api_doc/Config/OtherFunction.html#config">13. 查看config配置</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/Predictor.html">Predictor 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/PredictorPool.html">PredictorPool 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/Tensor.html">Tensor 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/python_api_doc/Enum.html">枚举类型</a></li>
</ul>
</li>
<li class="toctree-l2"><a class="reference internal" href="../../api_reference/cxx_api_doc/cxx_api_index.html">C++ API 文档</a><ul>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/CreatePredictor.html">CreatePredictor 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/CreatePredictor.html#getversion">GetVersion 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/CreatePredictor.html#converttomixedprecision">ConvertToMixedPrecision 方法</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config_index.html">Config 类</a><ul>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/ConfigClass.html">1. Config 构造函数</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/ModelConfig.html">2. 设置预测模型</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/CPUConfig.html">3. 使用 CPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/GPUConfig.html">4. 使用 GPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/XPUConfig.html">5. 使用 XPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/ORTConfig.html">6. 使用 ONNXRuntime 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/IPUConfig.html">7. 使用 IPU 进行预测</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OptimConfig.html">8. 设置模型优化方法</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html">9. 启用内存优化</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html#shezhihuancunlujing">10. 设置缓存路径</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html#fc-padding">11. FC Padding</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html#profile">12. Profile 设置</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html#log">13. Log 设置</a></li>
<li class="toctree-l4"><a class="reference internal" href="../../api_reference/cxx_api_doc/Config/OtherFunction.html#config">14. 查看config配置</a></li>
</ul>
</li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/PaddlePassBuilder.html">PaddlePassBuilder 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/Predictor.html">Predictor 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/PredictorPool.html">PredictorPool 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/Tensor.html">Tensor 类</a></li>
<li class="toctree-l3"><a class="reference internal" href="../../api_reference/cxx_api_doc/Enum.html">枚举类型</a></li>
</ul>
</li>
</ul>
</li>
<li class="toctree-l1"><a class="reference internal" href="../../faq/index_faq.html">常见问题与解答</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../../faq/faq_env_compile.html">环境与编译问题</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../faq/faq_runtime_error.html">运行报错</a></li>
<li class="toctree-l2"><a class="reference internal" href="../../faq/faq_profiling.html">精度与性能</a></li>
</ul>
</li>
</ul>

        </div>
      </div>
      <div class="toc" id="navigation-toc">
        <ul>
<li><a class="reference internal" href="#">下载安装 Linux 推理库</a><ul>
<li><a class="reference internal" href="#c">C++ 推理库</a></li>
<li><a class="reference internal" href="#python">Python 推理库</a></li>
</ul>
</li>
<li><a class="reference internal" href="#windows">下载安装 Windows 推理库</a><ul>
<li><a class="reference internal" href="#id1">C++ 推理库</a></li>
</ul>
</li>
<li><a class="reference internal" href="#mac">下载安装 Mac 推理库</a><ul>
<li><a class="reference internal" href="#id2">C++ 推理库</a></li>
</ul>
</li>
</ul>

      </div>
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap"><nav class="wy-nav-top" aria-label="Mobile navigation menu" >
          <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
          <a href="../../index.html">Paddle-Inference</a>
      </nav>

      <div class="wy-nav-content">
        <div class="rst-content">
          

















<div role="navigation" aria-label="breadcrumbs navigation">

  <ul class="wy-breadcrumbs">
    
      <li><a href="../../index.html" class="icon icon-home"></a> &raquo;</li>
        
          <li><a href="../index_guides.html">使用指南</a> &raquo;</li>
        
          <li><a href="index_install.html">安装指南</a> &raquo;</li>
        
      <li>下载安装 Linux 推理库</li>
    
    
      <li class="wy-breadcrumbs-aside">
        
          
            <a href="../../_sources/guides/install/download_lib.md.txt" rel="nofollow"> View page source</a>
          
        
      </li>
    
  </ul>

  
  <hr/>
</div>
          <div role="main" class="document" itemscope="itemscope" itemtype="http://schema.org/Article">
           <div itemprop="articleBody">
             
  <div class="section" id="linux">
<h1>下载安装 Linux 推理库<a class="headerlink" href="#linux" title="Permalink to this headline">¶</a></h1>
<div class="section" id="c">
<h2>C++ 推理库<a class="headerlink" href="#c" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>预编译包使用方式见：<a class="reference internal" href="../quick_start/cpp_demo.html"><span class="doc">推理示例（C++）</span></a></p></li>
</ul>
<table border="1" class="docutils">
<thead>
<tr>
<th>硬件后端</th>
<th>是否打开 avx</th>
<th>数学库</th>
<th>gcc 版本</th>
<th>CUDA/cuDNN/TensorRT 版本</th>
<th align="left">推理库(3.0.0 版本)</th>
</tr>
</thead>
<tbody>
<tr>
<td>CPU</td>
<td>是</td>
<td>MKL</td>
<td>8.2</td>
<td>-</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Linux/CPU/gcc8.2_avx_mkl/paddle_inference.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>GPU</td>
<td>是</td>
<td>MKL</td>
<td>11.2</td>
<td>CUDA11.8/cuDNN8.9/TensorRT8.6</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Linux/GPU/x86-64_gcc11.2_avx_mkl_cuda11.8_cudnn8.9.7-trt8.6.1.6/paddle_inference.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>GPU</td>
<td>是</td>
<td>MKL</td>
<td>11.2</td>
<td>CUDA12.6/cuDNN9.5/TensorRT10.5</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Linux/GPU/x86-64_gcc11.2_avx_mkl_cuda12.6_cudnn9.5.1-trt10.5.0.18/paddle_inference.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>Jetson(all)</td>
<td>-</td>
<td>-</td>
<td>9.4</td>
<td>Jetpack 5.1.2</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Jetson/jetpack5.1.2_gcc9.4/all/paddle_inference_install_dir.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>Jetson(Xavier)</td>
<td>-</td>
<td>-</td>
<td>9.4</td>
<td>Jetpack 5.1.2</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Jetson/jetpack5.1.2_gcc9.4/xavier/paddle_inference_install_dir.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>Jetson(orin)</td>
<td>-</td>
<td>-</td>
<td>9.4</td>
<td>Jetpack 5.1.2</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Jetson/jetpack5.1.2_gcc9.4/orin/paddle_inference_install_dir.tgz">paddle_inference.tgz</a></td>
</tr>
</tbody>
</table></div>
<div class="section" id="python">
<h2>Python 推理库<a class="headerlink" href="#python" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>预编译包使用方式见：<a class="reference internal" href="../quick_start/python_demo.html"><span class="doc">推理示例（Python）</span></a></p></li>
</ul>
<table border="1" class="docutils">
<thead>
<tr>
<th align="left">版本说明</th>
<th align="left">python3.8</th>
</tr>
</thead>
<tbody>
<tr>
<td align="left">Jetpack5.1.2: nv-jetson-cuda11.4-cudnn8.6.0-trt8.5.2-jetpack5.1.2-all</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/python/Jetson/jetpack5.1.2_gcc9.4/all/paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl">paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl</a></td>
</tr>
<tr>
<td align="left">Jetpack5.1.2: nv-jetson-cuda11.4-cudnn8.6.0-trt8.5.2-jetpack5.1.2-xavier</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/python/Jetson/jetpack5.1.2_gcc9.4/xavier/paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl">paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl</a></td>
</tr>
<tr>
<td align="left">Jetpack5.1.2: nv-jetson-cuda11.4-cudnn8.6.0-trt8.5.2-jetpack5.1.2-orin</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/python/Jetson/jetpack5.1.2_gcc9.4/orin/paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl">paddlepaddle_gpu-3.0.0-cp38-cp38-linux_aarch64.whl</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="section" id="windows">
<h1>下载安装 Windows 推理库<a class="headerlink" href="#windows" title="Permalink to this headline">¶</a></h1>
<div class="section" id="id1">
<h2>C++ 推理库<a class="headerlink" href="#id1" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>预编译包使用方式见：<a class="reference internal" href="../quick_start/cpp_demo.html"><span class="doc">推理示例（C++）</span></a></p></li>
</ul>
<table border="1" class="docutils">
<thead>
<tr>
<th>硬件后端</th>
<th>是否使用 avx</th>
<th align="left">编译器</th>
<th align="left">CUDA/cuDNN/TensorRT 版本</th>
<th align="left">数学库</th>
<th align="left">推理库(3.0.0 版本)</th>
</tr>
</thead>
<tbody>
<tr>
<td>CPU</td>
<td>是</td>
<td align="left">MSVC 2019</td>
<td align="left">-</td>
<td align="left">MKL</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/Windows/CPU/x86-64_avx-mkl-vs2019/paddle_inference.zip">paddle_inference.zip</a></td>
</tr>
</tbody>
</table></div>
</div>
<div class="section" id="mac">
<h1>下载安装 Mac 推理库<a class="headerlink" href="#mac" title="Permalink to this headline">¶</a></h1>
<div class="section" id="id2">
<h2>C++ 推理库<a class="headerlink" href="#id2" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li><p>预编译包使用方式见：<a class="reference internal" href="../quick_start/cpp_demo.html"><span class="doc">推理示例（C++）</span></a></p></li>
</ul>
<table border="1" class="docutils">
<thead>
<tr>
<th>硬件后端</th>
<th>是否打开 avx</th>
<th>数学库</th>
<th align="left">推理库(3.0.0 版本)</th>
</tr>
</thead>
<tbody>
<tr>
<td>X86_64</td>
<td>是</td>
<td>Accelerate BLAS</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/MacOS/x86-64_clang_avx_accelerate_blas/paddle_inference.tgz">paddle_inference.tgz</a></td>
</tr>
<tr>
<td>m1</td>
<td>否</td>
<td>Accelerate BLAS</td>
<td align="left"><a href="https://paddle-inference-lib.bj.bcebos.com/3.0.0/cxx_c/MacOS/m1_clang_noavx_accelerate_blas/paddle_inference.tgz">paddle_inference.tgz</a></td>
</tr>
</tbody>
</table></div>
</div>


           </div>
          </div>
          <footer>
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
        <a href="compile/index_compile.html" class="btn btn-neutral float-right" title="源码编译" accesskey="n" rel="next">Next <span class="fa fa-arrow-circle-right" aria-hidden="true"></span></a>
        <a href="cpp_install.html" class="btn btn-neutral float-left" title="安装 C++ API" accesskey="p" rel="prev"><span class="fa fa-arrow-circle-left" aria-hidden="true"></span> Previous</a>
    </div>

  <hr/>

  <div role="contentinfo">
    <p>
        &#169; Copyright 2020, Paddle-Inference Developer.

    </p>
  </div>
    
    
    
    Built with <a href="https://www.sphinx-doc.org/">Sphinx</a> using a
    
    <a href="https://github.com/readthedocs/sphinx_rtd_theme">theme</a>
    
    provided by <a href="https://readthedocs.org">Read the Docs</a>. 

</footer>
        </div>
      </div>
    </section>
  </div>
  <script>
      jQuery(function () {
          SphinxRtdTheme.Navigation.enable(true);
      });
  </script> 

</body>
</html></div></div></body></html>