package handlers

import (
	"net/http"
	"path/filepath"

	"ocr-server/internal/config"
	"ocr-server/internal/utils"

	"github.com/gin-gonic/gin"
)

// ScreenshotHandler 截图处理器
type ScreenshotHandler struct {
	screenshotManager *utils.ScreenshotManager
}

// NewScreenshotHandler 创建新的截图处理器
func NewScreenshotHandler(cfg *config.AppConfig) *ScreenshotHandler {
	return &ScreenshotHandler{
		screenshotManager: utils.NewScreenshotManager(cfg),
	}
}

// HandleScreenshot 处理截图请求（使用快速截图）
func (h *ScreenshotHandler) HandleScreenshot(c *gin.Context) {
	// 使用快速截图方法
	screenshotPath, err := h.screenshotManager.TakeScreenshotFast()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "快速截图失败: " + err.Error(),
		})
		return
	}

	// 从完整路径中提取文件名
	filename := filepath.Base(screenshotPath)

	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success":  true,
		"filename": filename,
		"url":      "/screenshots/" + filename,
		"message":  "快速截图成功",
	})
}
