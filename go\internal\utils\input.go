package utils

import (
	"fmt"
	"os/exec"
)

// InputManager 输入管理器
type InputManager struct{}

// NewInputManager 创建新的输入管理器
func NewInputManager() *InputManager {
	return &InputManager{}
}

// Click 点击指定坐标（优化版本）
func (im *InputManager) Click(x, y int) error {
	// 使用更快的点击命令，减少延迟
	cmd := exec.Command("input", "tap", fmt.Sprintf("%d", x), fmt.Sprintf("%d", y))

	// 设置较短的超时，避免卡死
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("点击失败: %v", err)
	}
	return nil
}

// ClickFast 快速点击（无等待）
func (im *InputManager) ClickFast(x, y int) error {
	// 异步执行点击，不等待结果
	cmd := exec.Command("input", "tap", fmt.Sprintf("%d", x), fmt.Sprintf("%d", y))
	return cmd.Start() // 使用Start而不是Run，不等待完成
}

// Swipe 滑动
func (im *InputManager) Swipe(x1, y1, x2, y2, duration int) error {
	cmd := exec.Command("input", "swipe",
		fmt.Sprintf("%d", x1),
		fmt.Sprintf("%d", y1),
		fmt.Sprintf("%d", x2),
		fmt.Sprintf("%d", y2),
		fmt.Sprintf("%d", duration))
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("滑动失败: %v", err)
	}
	return nil
}

// InputText 输入文本
func (im *InputManager) InputText(text string) error {
	cmd := exec.Command("input", "text", text)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("输入失败: %v", err)
	}
	return nil
}

// KeyEvent 发送按键事件
func (im *InputManager) KeyEvent(keyCode int) error {
	cmd := exec.Command("input", "keyevent", fmt.Sprintf("%d", keyCode))
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("按键失败: %v", err)
	}
	return nil
}

// Back 返回键
func (im *InputManager) Back() error {
	return im.KeyEvent(4) // KEYCODE_BACK
}

// Home 主页键
func (im *InputManager) Home() error {
	return im.KeyEvent(3) // KEYCODE_HOME
}

// Menu 菜单键
func (im *InputManager) Menu() error {
	return im.KeyEvent(82) // KEYCODE_MENU
}
