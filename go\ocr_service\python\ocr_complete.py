#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的OCR解决方案
使用ppocr-onnx包进行文字识别，支持中文文件名，输出结果到文件
"""

import cv2
import os
import json
import argparse
import numpy as np
from ppocronnx.predict_system import TextSystem


def process_image(image_path):
    """处理单张图片（简化版本，仅用于控制台输出）"""
    # 初始化OCR系统
    print("正在初始化OCR系统...")
    text_sys = TextSystem()
    print("OCR系统初始化成功！")
    
    # 检查图片是否存在
    if not os.path.exists(image_path):
        print(f"错误：图片文件 {image_path} 不存在")
        return None
    
    print(f"正在处理图片: {image_path}")
    
    # 读取图片 - 使用中文路径兼容方式
    try:
        # 使用numpy读取中文路径
        img_array = np.fromfile(image_path, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        
        if img is None:
            # 尝试直接读取
            img = cv2.imread(image_path, cv2.IMREAD_COLOR)
            
        if img is None:
            print(f"错误：无法读取图片 {image_path}")
            return None
    except Exception as e:
        print(f"读取图片时发生错误: {e}")
        return None
    
    print(f"图片尺寸: {img.shape}")
    
    # 进行OCR识别
    print("正在进行OCR识别...")
    results = text_sys.detect_and_ocr(img)
    
    # 输出结果到控制台
    print("\n=== OCR识别结果 ===")
    if results:
        print(f"共识别出 {len(results)} 个文本块：\n")
        for i, boxed_result in enumerate(results, 1):
            print(f"{i}. 文本: {boxed_result.ocr_text}")
            print(f"   置信度: {boxed_result.score:.3f}")
            print()
    else:
        print("未检测到任何文本")
        return None
    
    print("OCR识别完成！")
    return results

def ocr_to_json(image_path):
    """OCR识别并返回JSON格式结果"""
    try:
        # 初始化OCR系统（静默模式，不输出调试信息）
        import warnings
        import io
        import contextlib
        
        warnings.filterwarnings('ignore')
        
        # 重定向stdout和stderr以禁用所有输出
        with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
            text_sys = TextSystem()
        
        # 检查图片是否存在
        if not os.path.exists(image_path):
            return {
                "success": False,
                "error": f"图片文件不存在: {image_path}",
                "results": []
            }
        
        # 读取图片 - 使用中文路径兼容方式
        try:
            # 使用numpy读取中文路径
            img_array = np.fromfile(image_path, dtype=np.uint8)
            img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
            
            if img is None:
                # 尝试直接读取
                img = cv2.imread(image_path, cv2.IMREAD_COLOR)
                
            if img is None:
                return {
                    "success": False,
                    "error": f"无法读取图片: {image_path}",
                    "results": []
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"读取图片时发生错误: {str(e)}",
                "results": []
            }
        
        # 进行OCR识别（静默模式）
        with contextlib.redirect_stdout(io.StringIO()), contextlib.redirect_stderr(io.StringIO()):
            results = text_sys.detect_and_ocr(img)
        
        # 格式化结果
        ocr_results = []
        if results:
            for i, boxed_result in enumerate(results):
                ocr_results.append({
                    "text": boxed_result.ocr_text,
                    "confidence": float(boxed_result.score),
                    "box": boxed_result.box.tolist() if hasattr(boxed_result.box, 'tolist') else [[float(x), float(y)] for x, y in boxed_result.box]
                })
        
        return {
            "success": True,
            "error": "",
            "image_path": image_path,
            "total_blocks": len(ocr_results),
            "results": ocr_results
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": f"OCR处理失败: {str(e)}",
            "results": []
        }

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OCR文字识别工具')
    parser.add_argument('mode', choices=['file', 'batch'], help='运行模式: file(单文件) 或 batch(批处理)')
    parser.add_argument('image_path', help='图片文件路径')
    parser.add_argument('--output', '-o', help='输出文件路径(可选)')
    parser.add_argument('--json-only', action='store_true', help='仅输出JSON格式到stdout')
    
    args = parser.parse_args()
    
    if args.mode == 'file':
        if args.json_only:
            # 仅输出JSON到stdout，供Go程序调用
            result = ocr_to_json(args.image_path)
            print(json.dumps(result, ensure_ascii=False))
        else:
            # 传统模式，保存文件并显示详细信息
            results = process_image(args.image_path)
            if results and args.output:
                # 如果指定了输出路径，保存JSON结果
                result = ocr_to_json(args.image_path)
                with open(args.output, 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print(f"结果已保存到: {args.output}")
    
    elif args.mode == 'batch':
        # 批处理模式保持原有逻辑
        image_files = [args.image_path]  # 可以扩展为多文件
        
        for image_path in image_files:
            if os.path.exists(image_path):
                print(f"\n{'='*60}")
                print(f"处理图片: {image_path}")
                print(f"{'='*60}")
                process_image(image_path)
            else:
                print(f"跳过不存在的文件: {image_path}")

if __name__ == "__main__":
    main()