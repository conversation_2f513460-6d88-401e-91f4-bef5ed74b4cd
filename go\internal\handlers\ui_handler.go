package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"ocr-server/internal/models"
	"ocr-server/internal/services"
)

// UIHandler UI自动化处理器
type UIHandler struct {
	uiService *services.IntegratedUIService
}

// NewUIHandler 创建新的UI处理器
func NewUIHandler(uiService *services.IntegratedUIService) *UIHandler {
	return &UIHandler{
		uiService: uiService,
	}
}

// HandleStartU2Service 处理启动U2服务请求
func (h *UIHandler) HandleStartU2Service(c *gin.Context) {
	result, err := h.uiService.StartU2Service()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "启动U2服务失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.<PERSON>(http.StatusOK, result)
	}
}

// HandleStopU2Service 处理停止U2服务请求
func (h *UIHandler) HandleStopU2Service(c *gin.Context) {
	result, err := h.uiService.StopU2Service()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "停止U2服务失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleGetU2ServiceStatus 处理获取U2服务状态请求
func (h *UIHandler) HandleGetU2ServiceStatus(c *gin.Context) {
	result, err := h.uiService.GetU2ServiceStatus()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取U2服务状态失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, result)
}

// HandleGetUIHierarchy 处理获取UI层级结构请求
func (h *UIHandler) HandleGetUIHierarchy(c *gin.Context) {
	result, err := h.uiService.GetUIHierarchy()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取UI层级失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleGenerateUIFingerprint 处理生成UI指纹请求
func (h *UIHandler) HandleGenerateUIFingerprint(c *gin.Context) {
	var req models.UIFingerprintGenerateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := h.uiService.GenerateUIFingerprint(req.X, req.Y)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成指纹失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleMatchUIElement 处理匹配UI元素请求
func (h *UIHandler) HandleMatchUIElement(c *gin.Context) {
	var req models.UIMatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := h.uiService.MatchUIElement(req.Template)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "匹配失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleMatchUIElementByFingerprint 处理根据指纹匹配UI元素请求
func (h *UIHandler) HandleMatchUIElementByFingerprint(c *gin.Context) {
	var req models.UIFingerprintMatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := h.uiService.MatchUIElementByFingerprint(req.Fingerprint)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "指纹匹配失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleGetAllUIElements 处理获取所有UI元素请求
func (h *UIHandler) HandleGetAllUIElements(c *gin.Context) {
	result, err := h.uiService.GetAllUIElements()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取UI元素失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}

// HandleGenerateEnhancedFingerprint 处理生成增强指纹请求
func (h *UIHandler) HandleGenerateEnhancedFingerprint(c *gin.Context) {
	var req struct {
		Element     services.ElementInfo `json:"element"`
		Description string               `json:"description"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := h.uiService.GenerateEnhancedFingerprint(req.Element, req.Description)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "生成增强指纹失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}
