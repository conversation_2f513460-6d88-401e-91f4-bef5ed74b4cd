package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"time"

	"ocr-server/internal/config"
	"ocr-server/internal/models"
	"ocr-server/internal/utils"
)

// IntegratedOCRService 集成的OCR服务，使用传统模式
type IntegratedOCRService struct {
	config            *config.AppConfig
	screenshotManager *utils.ScreenshotManager
	inputManager      *utils.InputManager

	// 重试和超时配置
	maxRetries     int
	requestTimeout time.Duration
}

// NewIntegratedOCRService 创建新的集成OCR服务
func NewIntegratedOCRService(cfg *config.AppConfig) *IntegratedOCRService {
	service := &IntegratedOCRService{
		config:            cfg,
		screenshotManager: utils.NewScreenshotManager(cfg),
		inputManager:      utils.NewInputManager(),
		maxRetries:        3,
		requestTimeout:    30 * time.Second,
	}

	// 确保截图目录存在
	service.ensureScreenshotDir()

	fmt.Println("OCR服务初始化完成，使用传统模式")
	return service
}

// ensureScreenshotDir 确保截图目录存在
func (ocr *IntegratedOCRService) ensureScreenshotDir() {
	if err := os.MkdirAll(ocr.config.ScreenshotDir, 0755); err != nil {
		fmt.Printf("警告: 创建截图目录失败: %v\n", err)
	}
}

// PerformOCR 执行OCR识别（使用传统模式）
func (ocr *IntegratedOCRService) PerformOCR() ([]map[string]interface{}, error) {
	// 使用传统模式
	var rawResult string
	var err error
	for i := 0; i < ocr.maxRetries; i++ {
		rawResult, err = ocr.callOCRService()
		if err == nil {
			break
		}
		if i < ocr.maxRetries-1 {
			time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
		}
	}

	if err != nil {
		return nil, fmt.Errorf("传统模式OCR失败: %v", err)
	}

	if rawResult == "" {
		return []map[string]interface{}{}, nil
	}

	texts := ocr.parseOCRResult(rawResult)
	return texts, nil
}

// PerformOCRClick 执行OCR识别并点击（使用传统模式）
func (ocr *IntegratedOCRService) PerformOCRClick(text string) (*models.OCRClickResponse, error) {
	if text == "" {
		return &models.OCRClickResponse{
			Success: false,
			Error:   "请输入要查找的文字",
		}, nil
	}

	// 使用传统模式进行搜索
	filepath, err := ocr.screenshotManager.TakeScreenshotFast()
	if err != nil {
		return nil, fmt.Errorf("快速截图失败: %v", err)
	}

	result, err := ocr.searchText(filepath, text)
	if err != nil {
		return &models.OCRClickResponse{
			Success: false,
			Error:   fmt.Sprintf("OCR搜索失败: %v", err),
		}, nil
	}

	// 计算点击坐标
	clickX := result.X + result.Width/2
	clickY := result.Y + result.Height/2

	// 执行点击
	if err := ocr.inputManager.Click(clickX, clickY); err != nil {
		return &models.OCRClickResponse{
			Success: false,
			Error:   fmt.Sprintf("点击失败: %v", err),
		}, nil
	}

	return &models.OCRClickResponse{
		Success:   true,
		Message:   fmt.Sprintf("成功找到并点击文字 '%s'", text),
		ClickX:    clickX,
		ClickY:    clickY,
		OCRResult: result.Text,
	}, nil
}

// SearchText 搜索文字（优先使用传统模式，避免卡死）
func (ocr *IntegratedOCRService) SearchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
	// 直接使用传统模式，避免常驻进程的搜索协议问题
	if imagePath == "" {
		// 如果没有提供图片路径，先截图
		var err error
		imagePath, err = ocr.screenshotManager.TakeScreenshotFast()
		if err != nil {
			return nil, fmt.Errorf("快速截图失败: %v", err)
		}
	}

	// 使用传统搜索方法，更稳定
	return ocr.searchText(imagePath, searchText)
}

// callOCRService 调用OCR服务（传统模式）
func (ocr *IntegratedOCRService) callOCRService() (string, error) {
	// 先截图
	imagePath, err := ocr.screenshotManager.TakeScreenshotFast()
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}

	// 调用OCR
	return ocr.callOCRServiceRaw(imagePath)
}

// 以下是传统模式的方法，作为回退方案
func (ocr *IntegratedOCRService) callOCRServiceRaw(imagePath string) (string, error) {
	// 添加调试信息
	fmt.Printf("OCR调用: 工具路径=%s, 图片路径=%s\n", ocr.config.PaddleOCRTool, imagePath)

	// 使用正确的OCR命令参数
	cmd := exec.Command(ocr.config.PaddleOCRTool, imagePath, "--json")
	cmd.Env = append(os.Environ(), "LD_LIBRARY_PATH="+ocr.config.LibPath)

	// 获取stderr以便调试
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("OCR调用失败: %v, stderr: %s\n", err, stderr.String())
		return "", fmt.Errorf("OCR服务调用失败: %v, stderr: %s", err, stderr.String())
	}

	result := string(output)
	fmt.Printf("OCR原始输出: %s\n", result)
	return result, nil
}

func (ocr *IntegratedOCRService) searchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
	// 添加超时控制，避免卡死
	cmd := exec.Command(ocr.config.PaddleOCRTool, imagePath, "--search", searchText)
	cmd.Env = append(os.Environ(), "LD_LIBRARY_PATH="+ocr.config.LibPath)

	// 设置超时
	done := make(chan error, 1)
	var output []byte
	var err error

	go func() {
		output, err = cmd.Output()
		done <- err
	}()

	// 等待完成或超时
	select {
	case err := <-done:
		if err != nil {
			return nil, fmt.Errorf("OCR搜索服务调用失败: %v", err)
		}
	case <-time.After(ocr.requestTimeout):
		cmd.Process.Kill()
		return nil, fmt.Errorf("OCR搜索超时")
	}

	var result models.OCRSearchResult
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("解析OCR搜索结果失败: %v", err)
	}

	if result.Text == "" {
		return nil, fmt.Errorf("未找到目标文字: %s", searchText)
	}

	return &result, nil
}

// parseOCRResult 解析OCR JSON结果为文字区域数组
func (ocr *IntegratedOCRService) parseOCRResult(rawResult string) []map[string]interface{} {
	var texts []map[string]interface{}

	if rawResult == "" {
		return texts
	}

	// 解析新的JSON格式: {"results": [...], "processing_time_ms": ..., ...}
	var ocrResponse map[string]interface{}
	if err := json.Unmarshal([]byte(rawResult), &ocrResponse); err == nil {
		// 获取results数组
		if results, ok := ocrResponse["results"].([]interface{}); ok {
			for _, item := range results {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if text, ok := itemMap["text"].(string); ok && text != "" {
						textInfo := map[string]interface{}{
							"text": text,
						}

						// 尝试解析坐标信息 - box格式: [[x1,y1], [x2,y2]]
						if box, ok := itemMap["box"].([]interface{}); ok && len(box) >= 2 {
							var coords [][]float64
							for _, point := range box {
								if p, ok := point.([]interface{}); ok && len(p) >= 2 {
									var x, y float64

									// 处理不同的数字类型
									switch xVal := p[0].(type) {
									case float64:
										x = xVal
									case int:
										x = float64(xVal)
									default:
										continue
									}

									switch yVal := p[1].(type) {
									case float64:
										y = yVal
									case int:
										y = float64(yVal)
									default:
										continue
									}

									coords = append(coords, []float64{x, y})
								}
							}

							if len(coords) >= 2 {
								// 计算边界框 - 取所有点的最小最大值
								minX := coords[0][0]
								maxX := coords[0][0]
								minY := coords[0][1]
								maxY := coords[0][1]

								for _, coord := range coords {
									if coord[0] < minX {
										minX = coord[0]
									}
									if coord[0] > maxX {
										maxX = coord[0]
									}
									if coord[1] < minY {
										minY = coord[1]
									}
									if coord[1] > maxY {
										maxY = coord[1]
									}
								}

								textInfo["bounds"] = map[string]interface{}{
									"x":      int(minX),
									"y":      int(minY),
									"width":  int(maxX - minX),
									"height": int(maxY - minY),
								}
							}
						}

						// 添加置信度信息
						if confidence, ok := itemMap["confidence"].(float64); ok {
							textInfo["confidence"] = confidence
						} else {
							textInfo["confidence"] = 0.9 // 默认置信度
						}

						texts = append(texts, textInfo)
					}
				}
			}
			return texts
		}
	}

	// 如果不是JSON格式，尝试解析文本格式
	lines := strings.Split(rawResult, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			textInfo := map[string]interface{}{
				"text": line,
				"bounds": map[string]interface{}{
					"x":      50,
					"y":      100 + i*40,
					"width":  len(line) * 20,
					"height": 30,
				},
				"confidence": 0.8,
			}
			texts = append(texts, textInfo)
		}
	}

	return texts
}
