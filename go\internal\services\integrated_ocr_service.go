package services

import (
	"bufio"
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"ocr-server/internal/config"
	"ocr-server/internal/models"
	"ocr-server/internal/utils"
)

// OCRMetrics OCR性能指标
type OCRMetrics struct {
	TotalRequests   int64         `json:"total_requests"`
	SuccessRequests int64         `json:"success_requests"`
	FailedRequests  int64         `json:"failed_requests"`
	AverageLatency  time.Duration `json:"average_latency"`
	LastRequestTime time.Time     `json:"last_request_time"`
	DaemonRestarts  int64         `json:"daemon_restarts"`
}

// IntegratedOCRService 集成的OCR服务，支持常驻模式
type IntegratedOCRService struct {
	config            *config.AppConfig
	screenshotManager *utils.ScreenshotManager
	inputManager      *utils.InputManager

	// OCR常驻进程管理
	ocrProcess *exec.Cmd
	ocrStdin   io.WriteCloser
	ocrStdout  io.ReadCloser
	ocrReader  *bufio.Reader
	ocrMutex   sync.Mutex
	ocrReady   int32 // 使用atomic操作

	// 缓存和性能监控
	cache      map[string]interface{}
	cacheMutex sync.RWMutex
	metrics    OCRMetrics

	// 重试和超时配置
	maxRetries     int
	requestTimeout time.Duration
	daemonTimeout  time.Duration
}

// NewIntegratedOCRService 创建新的集成OCR服务
func NewIntegratedOCRService(cfg *config.AppConfig) *IntegratedOCRService {
	service := &IntegratedOCRService{
		config:            cfg,
		screenshotManager: utils.NewScreenshotManager(cfg),
		inputManager:      utils.NewInputManager(),
		cache:             make(map[string]interface{}),
		maxRetries:        3,
		requestTimeout:    30 * time.Second,
		daemonTimeout:     10 * time.Second,
	}

	// 确保截图目录存在
	service.ensureScreenshotDir()

	// 尝试启动常驻进程
	if err := service.startOCRDaemon(); err != nil {
		fmt.Printf("警告: 启动OCR常驻进程失败，将使用传统模式: %v\n", err)
	} else {
		// 只有常驻进程启动成功才启动后台截图缓存
		go service.backgroundScreenshotCache()
		// 启动健康检查
		go service.healthCheck()
	}

	return service
}

// ensureScreenshotDir 确保截图目录存在
func (ocr *IntegratedOCRService) ensureScreenshotDir() {
	if err := os.MkdirAll(ocr.config.ScreenshotDir, 0755); err != nil {
		fmt.Printf("警告: 创建截图目录失败: %v\n", err)
	}
}

// backgroundScreenshotCache 后台截图缓存（简化版）
func (ocr *IntegratedOCRService) backgroundScreenshotCache() {
	// 简单的预热，不持续缓存
	time.Sleep(1 * time.Second)
	ocr.screenshotManager.TakeScreenshotFast()
	fmt.Println("OCR截图预热完成")
}

// healthCheck 健康检查，定期检查OCR进程状态
func (ocr *IntegratedOCRService) healthCheck() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for range ticker.C {
		if atomic.LoadInt32(&ocr.ocrReady) == 0 {
			continue
		}

		// 发送心跳检查
		ocr.ocrMutex.Lock()
		if ocr.ocrStdin != nil {
			_, err := ocr.ocrStdin.Write([]byte("PING\n"))
			if err != nil {
				fmt.Printf("OCR进程心跳检查失败，尝试重启: %v\n", err)
				ocr.restartOCRDaemon()
			}
		}
		ocr.ocrMutex.Unlock()
	}
}

// restartOCRDaemon 重启OCR常驻进程
func (ocr *IntegratedOCRService) restartOCRDaemon() {
	fmt.Println("正在重启OCR常驻进程...")
	atomic.AddInt64(&ocr.metrics.DaemonRestarts, 1)

	// 停止当前进程
	ocr.stopOCRDaemonInternal()

	// 等待一段时间后重启
	time.Sleep(2 * time.Second)

	// 重新启动
	if err := ocr.startOCRDaemon(); err != nil {
		fmt.Printf("重启OCR常驻进程失败: %v\n", err)
	} else {
		fmt.Println("OCR常驻进程重启成功")
	}
}

// startOCRDaemon 启动OCR常驻进程
func (ocr *IntegratedOCRService) startOCRDaemon() error {
	ocr.ocrMutex.Lock()
	defer ocr.ocrMutex.Unlock()

	// 启动OCR daemon进程
	cmd := exec.Command(ocr.config.PaddleOCRTool, "--daemon")
	cmd.Env = append(os.Environ(), "LD_LIBRARY_PATH="+ocr.config.LibPath)

	// 设置管道
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return fmt.Errorf("创建stdin管道失败: %v", err)
	}

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		stdin.Close()
		return fmt.Errorf("创建stdout管道失败: %v", err)
	}

	// 启动进程
	if err := cmd.Start(); err != nil {
		stdin.Close()
		stdout.Close()
		return fmt.Errorf("启动OCR daemon失败: %v", err)
	}

	ocr.ocrProcess = cmd
	ocr.ocrStdin = stdin
	ocr.ocrStdout = stdout
	ocr.ocrReader = bufio.NewReader(stdout)

	// 等待daemon就绪信号
	go ocr.waitForOCRReady()

	// 等待最多10秒
	for i := 0; i < 100; i++ {
		if atomic.LoadInt32(&ocr.ocrReady) == 1 {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	if atomic.LoadInt32(&ocr.ocrReady) == 0 {
		ocr.stopOCRDaemonInternal()
		return fmt.Errorf("OCR daemon启动超时")
	}

	fmt.Println("OCR常驻进程启动成功")
	return nil
}

// waitForOCRReady 等待OCR服务就绪
func (ocr *IntegratedOCRService) waitForOCRReady() {
	for {
		line, err := ocr.ocrReader.ReadString('\n')
		if err != nil {
			return
		}

		if strings.Contains(line, "OCR_DAEMON_READY") {
			atomic.StoreInt32(&ocr.ocrReady, 1)
			return
		}
	}
}

// stopOCRDaemon 停止OCR常驻进程
func (ocr *IntegratedOCRService) stopOCRDaemon() {
	ocr.ocrMutex.Lock()
	defer ocr.ocrMutex.Unlock()
	ocr.stopOCRDaemonInternal()
}

// stopOCRDaemonInternal 内部停止方法，不获取锁
func (ocr *IntegratedOCRService) stopOCRDaemonInternal() {
	if ocr.ocrProcess != nil {
		if ocr.ocrStdin != nil {
			ocr.ocrStdin.Write([]byte("QUIT\n"))
			ocr.ocrStdin.Close()
		}
		if ocr.ocrStdout != nil {
			ocr.ocrStdout.Close()
		}
		ocr.ocrProcess.Wait()
		ocr.ocrProcess = nil
	}
	atomic.StoreInt32(&ocr.ocrReady, 0)
}

// callOCRDaemonMemory 内存模式调用OCR常驻进程（真正的零文件I/O）
func (ocr *IntegratedOCRService) callOCRDaemonMemory(command string) (string, error) {
	ocr.ocrMutex.Lock()
	defer ocr.ocrMutex.Unlock()

	if atomic.LoadInt32(&ocr.ocrReady) == 0 || ocr.ocrStdin == nil {
		return "", fmt.Errorf("OCR daemon未就绪")
	}

	// 直接从内存获取截图数据
	screenshotData, err := ocr.screenshotManager.TakeScreenshotToMemory()
	if err != nil {
		return "", fmt.Errorf("内存截图失败: %v", err)
	}

	// 发送命令（使用STDIN标识）
	cmdLine := fmt.Sprintf("%s STDIN\n", command)
	if _, err := ocr.ocrStdin.Write([]byte(cmdLine)); err != nil {
		return "", fmt.Errorf("发送命令失败: %v", err)
	}

	// 发送图片数据长度
	dataLength := int32(len(screenshotData))
	if err := binary.Write(ocr.ocrStdin, binary.LittleEndian, dataLength); err != nil {
		return "", fmt.Errorf("发送数据长度失败: %v", err)
	}

	// 发送图片数据
	if _, err := ocr.ocrStdin.Write(screenshotData); err != nil {
		return "", fmt.Errorf("发送图片数据失败: %v", err)
	}

	// 读取响应
	response, err := ocr.ocrReader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	return strings.TrimSpace(response), nil
}

// callOCRDaemon 调用OCR常驻进程（兼容旧接口）
func (ocr *IntegratedOCRService) callOCRDaemon(imagePath, command string) (string, error) {
	ocr.ocrMutex.Lock()
	defer ocr.ocrMutex.Unlock()

	if atomic.LoadInt32(&ocr.ocrReady) == 0 || ocr.ocrStdin == nil {
		return "", fmt.Errorf("OCR daemon未就绪")
	}

	// 发送命令
	cmdLine := fmt.Sprintf("%s %s\n", command, imagePath)
	if _, err := ocr.ocrStdin.Write([]byte(cmdLine)); err != nil {
		return "", fmt.Errorf("发送命令失败: %v", err)
	}

	// 读取响应
	response, err := ocr.ocrReader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	return strings.TrimSpace(response), nil
}

// PerformOCR 执行OCR识别（只使用快速常驻模式）
func (ocr *IntegratedOCRService) PerformOCR() ([]map[string]interface{}, error) {
	start := time.Now()
	defer func() {
		ocr.updateMetrics(time.Since(start), true)
	}()

	// 只使用快速常驻进程 + 缓存截图
	if atomic.LoadInt32(&ocr.ocrReady) == 0 {
		ocr.updateMetrics(time.Since(start), false)
		return nil, fmt.Errorf("OCR常驻进程未就绪")
	}

	// 带重试的OCR调用
	var rawResult string
	var err error
	for i := 0; i < ocr.maxRetries; i++ {
		rawResult, err = ocr.callOCRDaemonMemory("OCR")
		if err == nil {
			break
		}
		if i < ocr.maxRetries-1 {
			time.Sleep(time.Duration(i+1) * 100 * time.Millisecond) // 指数退避
		}
	}

	if err != nil {
		ocr.updateMetrics(time.Since(start), false)
		return nil, fmt.Errorf("内存模式OCR调用失败: %v", err)
	}

	if rawResult == "" {
		return []map[string]interface{}{}, nil
	}

	texts := ocr.parseOCRResult(rawResult)
	return texts, nil
}

// PerformOCRClick 执行OCR识别并点击（使用常驻进程）
func (ocr *IntegratedOCRService) PerformOCRClick(text string) (*models.OCRClickResponse, error) {
	start := time.Now()
	defer func() {
		ocr.updateMetrics(time.Since(start), true)
	}()

	if text == "" {
		return &models.OCRClickResponse{
			Success: false,
			Error:   "请输入要查找的文字",
		}, nil
	}

	// 优先使用内存模式搜索
	if atomic.LoadInt32(&ocr.ocrReady) == 1 {
		var result *models.OCRSearchResult
		var err error

		// 带重试的搜索
		for i := 0; i < ocr.maxRetries; i++ {
			result, err = ocr.SearchText("", text)
			if err == nil && result != nil {
				break
			}
			if i < ocr.maxRetries-1 {
				time.Sleep(time.Duration(i+1) * 100 * time.Millisecond)
			}
		}

		if err == nil && result != nil {
			// 计算点击坐标
			clickX := result.X + result.Width/2
			clickY := result.Y + result.Height/2

			// 执行点击
			if err := ocr.inputManager.Click(clickX, clickY); err != nil {
				return &models.OCRClickResponse{
					Success: false,
					Error:   fmt.Sprintf("点击失败: %v", err),
				}, nil
			}

			return &models.OCRClickResponse{
				Success:   true,
				Message:   fmt.Sprintf("成功找到并点击文字 '%s'", text),
				ClickX:    clickX,
				ClickY:    clickY,
				OCRResult: result.Text,
			}, nil
		}
	}

	// 回退到传统模式
	filepath, err := ocr.screenshotManager.TakeScreenshotFast()
	if err != nil {
		ocr.updateMetrics(time.Since(start), false)
		return nil, fmt.Errorf("快速截图失败: %v", err)
	}

	result, err := ocr.searchText(filepath, text)
	if err != nil {
		ocr.updateMetrics(time.Since(start), false)
		return &models.OCRClickResponse{
			Success: false,
			Error:   fmt.Sprintf("OCR搜索失败: %v", err),
		}, nil
	}

	// 计算点击坐标
	clickX := result.X + result.Width/2
	clickY := result.Y + result.Height/2

	// 执行点击
	if err := ocr.inputManager.Click(clickX, clickY); err != nil {
		return &models.OCRClickResponse{
			Success: false,
			Error:   fmt.Sprintf("点击失败: %v", err),
		}, nil
	}

	return &models.OCRClickResponse{
		Success:   true,
		Message:   fmt.Sprintf("成功找到并点击文字 '%s'", text),
		ClickX:    clickX,
		ClickY:    clickY,
		OCRResult: result.Text,
	}, nil
}

// SearchText 搜索文字（只使用快速常驻进程）
func (ocr *IntegratedOCRService) SearchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
	if atomic.LoadInt32(&ocr.ocrReady) == 0 {
		return nil, fmt.Errorf("OCR常驻进程未就绪")
	}

	// 使用内存模式搜索
	response, err := ocr.callOCRDaemonMemory(fmt.Sprintf("SEARCH %s", searchText))
	if err != nil {
		return nil, fmt.Errorf("内存模式搜索失败: %v", err)
	}

	var result models.OCRSearchResult
	if err := json.Unmarshal([]byte(response), &result); err != nil {
		return nil, fmt.Errorf("解析搜索结果失败: %v", err)
	}

	return &result, nil
}

// 以下是传统模式的方法，作为回退方案
func (ocr *IntegratedOCRService) callOCRServiceRaw(imagePath string) (string, error) {
	// 添加调试信息
	fmt.Printf("OCR调用: 工具路径=%s, 图片路径=%s\n", ocr.config.PaddleOCRTool, imagePath)

	// 使用正确的OCR命令参数
	cmd := exec.Command(ocr.config.PaddleOCRTool, imagePath, "--json")
	cmd.Env = append(os.Environ(), "LD_LIBRARY_PATH="+ocr.config.LibPath)

	// 获取stderr以便调试
	var stderr bytes.Buffer
	cmd.Stderr = &stderr

	output, err := cmd.Output()
	if err != nil {
		fmt.Printf("OCR调用失败: %v, stderr: %s\n", err, stderr.String())
		return "", fmt.Errorf("OCR服务调用失败: %v, stderr: %s", err, stderr.String())
	}

	result := string(output)
	fmt.Printf("OCR原始输出: %s\n", result)
	return result, nil
}

func (ocr *IntegratedOCRService) searchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
	cmd := exec.Command(ocr.config.PaddleOCRTool, imagePath, "--search", searchText)
	cmd.Env = append(os.Environ(), "LD_LIBRARY_PATH="+ocr.config.LibPath)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("OCR搜索服务调用失败: %v", err)
	}

	var result models.OCRSearchResult
	if err := json.Unmarshal(output, &result); err != nil {
		return nil, fmt.Errorf("解析OCR搜索结果失败: %v", err)
	}

	if result.Text == "" {
		return nil, fmt.Errorf("未找到目标文字: %s", searchText)
	}

	return &result, nil
}

// parseOCRResult 解析OCR JSON结果为文字区域数组
func (ocr *IntegratedOCRService) parseOCRResult(rawResult string) []map[string]interface{} {
	var texts []map[string]interface{}

	if rawResult == "" {
		return texts
	}

	// 解析新的JSON格式: {"results": [...], "processing_time_ms": ..., ...}
	var ocrResponse map[string]interface{}
	if err := json.Unmarshal([]byte(rawResult), &ocrResponse); err == nil {
		// 获取results数组
		if results, ok := ocrResponse["results"].([]interface{}); ok {
			for _, item := range results {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if text, ok := itemMap["text"].(string); ok && text != "" {
						textInfo := map[string]interface{}{
							"text": text,
						}

						// 尝试解析坐标信息 - box格式: [[x1,y1], [x2,y2]]
						if box, ok := itemMap["box"].([]interface{}); ok && len(box) >= 2 {
							var coords [][]float64
							for _, point := range box {
								if p, ok := point.([]interface{}); ok && len(p) >= 2 {
									var x, y float64

									// 处理不同的数字类型
									switch xVal := p[0].(type) {
									case float64:
										x = xVal
									case int:
										x = float64(xVal)
									default:
										continue
									}

									switch yVal := p[1].(type) {
									case float64:
										y = yVal
									case int:
										y = float64(yVal)
									default:
										continue
									}

									coords = append(coords, []float64{x, y})
								}
							}

							if len(coords) >= 2 {
								// 计算边界框 - 取所有点的最小最大值
								minX := coords[0][0]
								maxX := coords[0][0]
								minY := coords[0][1]
								maxY := coords[0][1]

								for _, coord := range coords {
									if coord[0] < minX {
										minX = coord[0]
									}
									if coord[0] > maxX {
										maxX = coord[0]
									}
									if coord[1] < minY {
										minY = coord[1]
									}
									if coord[1] > maxY {
										maxY = coord[1]
									}
								}

								textInfo["bounds"] = map[string]interface{}{
									"x":      int(minX),
									"y":      int(minY),
									"width":  int(maxX - minX),
									"height": int(maxY - minY),
								}
							}
						}

						// 添加置信度信息
						if confidence, ok := itemMap["confidence"].(float64); ok {
							textInfo["confidence"] = confidence
						} else {
							textInfo["confidence"] = 0.9 // 默认置信度
						}

						texts = append(texts, textInfo)
					}
				}
			}
			return texts
		}
	}

	// 如果不是JSON格式，尝试解析文本格式
	lines := strings.Split(rawResult, "\n")
	for i, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" {
			textInfo := map[string]interface{}{
				"text": line,
				"bounds": map[string]interface{}{
					"x":      50,
					"y":      100 + i*40,
					"width":  len(line) * 20,
					"height": 30,
				},
				"confidence": 0.8,
			}
			texts = append(texts, textInfo)
		}
	}

	return texts
}

// updateMetrics 更新性能指标
func (ocr *IntegratedOCRService) updateMetrics(duration time.Duration, success bool) {
	atomic.AddInt64(&ocr.metrics.TotalRequests, 1)
	if success {
		atomic.AddInt64(&ocr.metrics.SuccessRequests, 1)
	} else {
		atomic.AddInt64(&ocr.metrics.FailedRequests, 1)
	}

	// 简单的移动平均
	ocr.metrics.AverageLatency = (ocr.metrics.AverageLatency + duration) / 2
	ocr.metrics.LastRequestTime = time.Now()
}

// GetMetrics 获取性能指标
func (ocr *IntegratedOCRService) GetMetrics() OCRMetrics {
	return OCRMetrics{
		TotalRequests:   atomic.LoadInt64(&ocr.metrics.TotalRequests),
		SuccessRequests: atomic.LoadInt64(&ocr.metrics.SuccessRequests),
		FailedRequests:  atomic.LoadInt64(&ocr.metrics.FailedRequests),
		AverageLatency:  ocr.metrics.AverageLatency,
		LastRequestTime: ocr.metrics.LastRequestTime,
		DaemonRestarts:  atomic.LoadInt64(&ocr.metrics.DaemonRestarts),
	}
}

// ClearCache 清理缓存
func (ocr *IntegratedOCRService) ClearCache() {
	ocr.cacheMutex.Lock()
	defer ocr.cacheMutex.Unlock()
	ocr.cache = make(map[string]interface{})
}

// Close 关闭服务，清理资源
func (ocr *IntegratedOCRService) Close() {
	// 停止常驻进程
	ocr.stopOCRDaemon()
	// 清理缓存
	ocr.ClearCache()
}
