package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"ocr-server/internal/models"
	"ocr-server/internal/services"
)

// OCRHandler OCR处理器
type OCRHandler struct {
	ocrService *services.IntegratedOCRService
}

// NewOCRHandler 创建新的OCR处理器
func NewOCRHandler(ocrService *services.IntegratedOCRService) *OCRHandler {
	return &OCRHandler{
		ocrService: ocrService,
	}
}

// HandleOCR 处理OCR识别请求
func (h *OCRHandler) HandleOCR(c *gin.Context) {
	texts, err := h.ocrService.PerformOCR()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "OCR识别失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"text":    texts,
		"texts":   texts, // 保持兼容性
	})
}

// HandleOCRClick 处理OCR点击请求
func (h *OCRHandler) HandleOCRClick(c *gin.Context) {
	var req models.OCRClickRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	result, err := h.ocrService.PerformOCRClick(req.Text)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "OCR点击失败: " + err.Error(),
		})
		return
	}

	if result.Success {
		c.JSON(http.StatusOK, result)
	} else {
		c.JSON(http.StatusOK, result)
	}
}
