package services

import (
	"fmt"
	"log"
	"sync"
	"time"

	"ocr-server/internal/config"
	"ocr-server/internal/models"
)

// ScriptEngine 引用主包中的脚本引擎
// 这里只是一个接口定义，实际实现在主包中
type ScriptEngine interface {
	Start() error
	Stop()
	Wait()
	IsRunning() bool
	GetStartTime() time.Time
	GetConfig() interface{}
}

// ScriptService 脚本管理服务
type ScriptService struct {
	config     *config.AppConfig
	engines    map[string]ScriptEngine
	mutex      sync.RWMutex
	ocrService *IntegratedOCRService
	uiService  *IntegratedUIService
}

// NewScriptService 创建新的脚本服务
func NewScriptService(cfg *config.AppConfig, ocrSvc *IntegratedOCRService, uiSvc *IntegratedUIService) *ScriptService {
	return &ScriptService{
		config:     cfg,
		engines:    make(map[string]ScriptEngine),
		ocrService: ocrSvc,
		uiService:  uiSvc,
	}
}

// StartScript 启动脚本
func (ss *ScriptService) StartScript(scriptPath string) (string, error) {
	ss.mutex.Lock()
	defer ss.mutex.Unlock()

	// 这里需要调用主包中的NewScriptEngine函数
	// 由于包结构限制，我们暂时返回一个错误提示
	return "", fmt.Errorf("脚本功能需要重构以支持集成服务")
}

// StartScriptFromConfig 从配置启动脚本
func (ss *ScriptService) StartScriptFromConfig(config interface{}) (string, error) {
	ss.mutex.Lock()
	defer ss.mutex.Unlock()

	// 这里需要调用主包中的NewScriptEngineFromConfig函数
	// 由于包结构限制，我们暂时返回一个错误提示
	return "", fmt.Errorf("脚本功能需要重构以支持集成服务")
}

// StopScript 停止脚本
func (ss *ScriptService) StopScript(scriptID string) error {
	ss.mutex.Lock()
	defer ss.mutex.Unlock()

	engine, exists := ss.engines[scriptID]
	if !exists {
		return fmt.Errorf("脚本不存在: %s", scriptID)
	}

	engine.Stop()
	delete(ss.engines, scriptID)
	log.Printf("脚本已停止: %s", scriptID)

	return nil
}

// GetScriptStatus 获取脚本状态
func (ss *ScriptService) GetScriptStatus() map[string]models.ScriptStatus {
	ss.mutex.RLock()
	defer ss.mutex.RUnlock()

	scripts := make(map[string]models.ScriptStatus)
	for id, engine := range ss.engines {
		status := "running"
		if !engine.IsRunning() {
			status = "stopped"
		}

		startTime := engine.GetStartTime()
		runningTime := time.Since(startTime).String()

		// 尝试获取配置信息
		configName := "Unknown"
		if config := engine.GetConfig(); config != nil {
			// 这里需要根据实际的配置结构来获取名称
			configName = "Script"
		}

		scripts[id] = models.ScriptStatus{
			ID:          id,
			Name:        configName,
			Status:      status,
			StartTime:   startTime,
			RunningTime: runningTime,
		}
	}

	return scripts
}

// ListAvailableScripts 列出可用脚本
func (ss *ScriptService) ListAvailableScripts() ([]interface{}, error) {
	// 这里应该扫描脚本目录并返回可用脚本列表
	// 暂时返回空列表
	return []interface{}{}, nil
}

// StopAllScripts 停止所有脚本
func (ss *ScriptService) StopAllScripts() error {
	ss.mutex.Lock()
	defer ss.mutex.Unlock()

	for id, engine := range ss.engines {
		engine.Stop()
		log.Printf("脚本已停止: %s", id)
	}

	// 清空引擎映射
	ss.engines = make(map[string]ScriptEngine)

	return nil
}
