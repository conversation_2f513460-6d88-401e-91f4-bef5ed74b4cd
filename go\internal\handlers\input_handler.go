package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"ocr-server/internal/config"
	"ocr-server/internal/models"
	"ocr-server/internal/utils"
)

// InputHandler 输入处理器
type InputHandler struct {
	inputManager *utils.InputManager
}

// NewInputHandler 创建新的输入处理器
func NewInputHandler(cfg *config.AppConfig) *InputHandler {
	return &InputHandler{
		inputManager: utils.NewInputManager(),
	}
}

// HandleClick 处理点击请求
func (h *InputHandler) HandleClick(c *gin.Context) {
	var req models.ClickRequest
	if err := c.ShouldBind<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.inputManager.Click(req.X, req.Y); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "点击失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "点击成功",
	})
}

// HandleSwipe 处理滑动请求
func (h *InputHandler) HandleSwipe(c *gin.Context) {
	var req models.SwipeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.inputManager.Swipe(req.X1, req.Y1, req.X2, req.Y2, req.Duration); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "滑动失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "滑动成功",
	})
}
