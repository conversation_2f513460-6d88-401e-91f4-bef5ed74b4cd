#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <opencv2/opencv.hpp>
#include <onnxruntime_cxx_api.h>
#include <algorithm>
#include <numeric>
#include <cmath>
#include <chrono>
#include <iomanip>
#include <thread>
#include <omp.h>
#include "jni/clipper.hpp"
using namespace ClipperLib;

// Function declarations
cv::Mat getRotateCropImage(const cv::Mat& img, const std::vector<cv::Point2f>& box);

class PaddleOCRDetector {
private:
    Ort::Session* session;
    Ort::Env env;
    Ort::SessionOptions session_options;
    std::vector<const char*> input_names;
    std::vector<const char*> output_names;
    
    // Pre-allocated buffers for better performance
    std::vector<float> input_buffer;
    cv::Mat resized_buffer;
    cv::Mat padded_buffer;
    
public:
    PaddleOCRDetector(const std::string& model_path) : env(ORT_LOGGING_LEVEL_ERROR, "PaddleOCRDetector") {
        // Configure session options for optimal performance
        session_options.SetIntraOpNumThreads(2);  // Reduced for better performance
        session_options.SetInterOpNumThreads(1);  // Sequential execution
        session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        session_options.SetExecutionMode(ExecutionMode::ORT_SEQUENTIAL);
        session_options.EnableCpuMemArena();
        session_options.EnableMemPattern();
        
        session = new Ort::Session(env, model_path.c_str(), session_options);
        
        Ort::AllocatorWithDefaultOptions allocator;
        size_t num_input_nodes = session->GetInputCount();
        size_t num_output_nodes = session->GetOutputCount();
        
        for (size_t i = 0; i < num_input_nodes; i++) {
            auto input_name = session->GetInputNameAllocated(i, allocator);
            input_names.push_back(strdup(input_name.get()));
        }
        
        for (size_t i = 0; i < num_output_nodes; i++) {
            auto output_name = session->GetOutputNameAllocated(i, allocator);
            output_names.push_back(strdup(output_name.get()));
        }
        
        // Pre-allocate buffers
        input_buffer.resize(3 * 960 * 960);
        resized_buffer = cv::Mat(960, 960, CV_8UC3);
        padded_buffer = cv::Mat(960, 960, CV_32FC3, cv::Scalar(0, 0, 0));
    }
    
    ~PaddleOCRDetector() {
        delete session;
    }
    
    std::vector<std::vector<cv::Point2f>> detect(const cv::Mat& image) {
        // Optimized preprocessing with reduced memory allocations
        int target_size = 960;
        
        // Calculate scale to keep aspect ratio
        float scale = std::min((float)target_size / image.cols, (float)target_size / image.rows);
        int new_width = (int)(image.cols * scale);
        int new_height = (int)(image.rows * scale);
        
        // Resize image keeping aspect ratio
        cv::Mat resized_img;
        cv::resize(image, resized_img, cv::Size(new_width, new_height));
        
        // Create padded image - reuse buffer and zero only necessary parts
        padded_buffer.setTo(0);
        int x_offset = (target_size - new_width) / 2;
        int y_offset = (target_size - new_height) / 2;
        
        // Convert and copy in one step with proper normalization
        cv::Mat roi = padded_buffer(cv::Rect(x_offset, y_offset, new_width, new_height));
        cv::Mat normalized;
        resized_img.convertTo(normalized, CV_32FC3, 1.0/255.0);
        
        // Apply PaddleOCR standard normalization (mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        // Note: OpenCV uses BGR, PaddleOCR params are for RGB, so we map accordingly
        std::vector<cv::Mat> channels(3);
        cv::split(normalized, channels);
        
        // Apply normalization per channel: (pixel - mean) / std
        // BGR format: map to RGB normalization params correctly
        // RGB params: mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]
        // BGR mapping: B->B, G->G, R->R (correct mapping)
        channels[0] = (channels[0] - 0.406f) / 0.225f;  // B channel with B params
        channels[1] = (channels[1] - 0.456f) / 0.224f;  // G channel with G params
        channels[2] = (channels[2] - 0.485f) / 0.229f;  // R channel with R params
        
        cv::merge(channels, roi);
        
        // Optimized tensor conversion with direct memory access
        float* input_data = input_buffer.data();
        const float* img_data = (const float*)padded_buffer.data;
        
        // Optimized single-pass BGR to RGB conversion
        const int total_pixels = 960 * 960;
        const float* src = (const float*)padded_buffer.data;
        float* r_ptr = input_data;
        float* g_ptr = input_data + total_pixels;
        float* b_ptr = input_data + 2 * total_pixels;
        
        for (int i = 0; i < total_pixels; ++i) {
            const int src_idx = i * 3;
            b_ptr[i] = src[src_idx];     // B
            g_ptr[i] = src[src_idx + 1]; // G
            r_ptr[i] = src[src_idx + 2]; // R
        }
        
        // Create input tensor
        std::vector<int64_t> input_shape = {1, 3, 960, 960};
        auto memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value input_tensor = Ort::Value::CreateTensor<float>(memory_info, input_data,
                                                                  input_buffer.size(), input_shape.data(), input_shape.size());
        
        // Run inference
        auto output_tensors = session->Run(Ort::RunOptions{nullptr}, input_names.data(), &input_tensor, 1, output_names.data(), output_names.size());
        
        // Optimized post-processing
        return postProcessDetection(output_tensors[0], image.cols, image.rows, new_width, new_height, x_offset, y_offset, scale);
    }
    
private:
    std::vector<std::vector<cv::Point2f>> postProcessDetection(Ort::Value& output_tensor, int orig_width, int orig_height,
                                                               int new_width, int new_height, int x_offset, int y_offset, float scale) {
        float* output_data = output_tensor.GetTensorMutableData<float>();
        cv::Mat output_map(960, 960, CV_32F, output_data);
        
        // DB post-processing parameters (optimized for small text detection)
        float thresh = 0.2f;  // Lower threshold for better small text detection
        float box_thresh = 0.4f;  // Lower box threshold to catch more candidates
        float unclip_ratio = 1.8f;  // Higher expansion for better text capture
        int min_size = 1;  // Minimum size to catch very small text like "跳过"
        
        // Optimized binary threshold with dilation (like Python version)
        cv::Mat binary_map;
        cv::threshold(output_map, binary_map, thresh, 255, cv::THRESH_BINARY);
        binary_map.convertTo(binary_map, CV_8U);
        
        // Apply dilation to improve text connectivity (use_dilation=True in Python)
        cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(2, 2));
        cv::dilate(binary_map, binary_map, kernel);
        
        // Find contours
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(binary_map, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);
        
        std::vector<std::vector<cv::Point2f>> text_boxes;
        text_boxes.reserve(contours.size());  // Pre-allocate
        
        // Calculate scale factors
        float scale_x = (float)orig_width / (float)new_width;
        float scale_y = (float)orig_height / (float)new_height;
        
        for (const auto& contour : contours) {
            if (contour.size() < 4) continue;
            
            // Quick area check before expensive operations
            double area = cv::contourArea(contour);
            if (area < min_size * min_size) continue;
            
            // Get minimum area rectangle
            cv::RotatedRect rect = cv::minAreaRect(contour);
            if (std::min(rect.size.width, rect.size.height) < min_size) continue;
            
            // Get box points
            cv::Point2f vertices[4];
            rect.points(vertices);
            
            // Quick score calculation using sampling instead of full mask
            float box_score = calculateBoxScore(output_map, vertices);
            if (box_score < box_thresh) continue;
            
            // Simplified unclip using direct polygon expansion
            std::vector<cv::Point2f> expanded_box = unclipBox(vertices, area, unclip_ratio, cv::arcLength(contour, true));
            
            // Transform coordinates back to original image space
            for (auto& point : expanded_box) {
                point.x = (point.x - x_offset) * scale_x;
                point.y = (point.y - y_offset) * scale_y;
                
                // Clamp to image bounds
                point.x = std::max(0.0f, std::min((float)orig_width - 1, point.x));
                point.y = std::max(0.0f, std::min((float)orig_height - 1, point.y));
            }
            
            if (expanded_box.size() == 4) {
                text_boxes.push_back(expanded_box);
            }
        }
        
        return text_boxes;
    }
    
    float calculateBoxScore(const cv::Mat& output_map, cv::Point2f vertices[4]) {
        // Sample points inside the box for faster score calculation
        float sum = 0.0f;
        int count = 0;
        
        // Get bounding box
        float min_x = std::min({vertices[0].x, vertices[1].x, vertices[2].x, vertices[3].x});
        float max_x = std::max({vertices[0].x, vertices[1].x, vertices[2].x, vertices[3].x});
        float min_y = std::min({vertices[0].y, vertices[1].y, vertices[2].y, vertices[3].y});
        float max_y = std::max({vertices[0].y, vertices[1].y, vertices[2].y, vertices[3].y});
        
        // Sample every 2 pixels for speed
        for (int y = (int)min_y; y <= (int)max_y; y += 2) {
            for (int x = (int)min_x; x <= (int)max_x; x += 2) {
                if (x >= 0 && x < 960 && y >= 0 && y < 960) {
                    if (cv::pointPolygonTest(std::vector<cv::Point2f>(vertices, vertices + 4), cv::Point2f(x, y), false) >= 0) {
                        sum += output_map.at<float>(y, x);
                        count++;
                    }
                }
            }
        }
        
        return count > 0 ? sum / count : 0.0f;
    }
    
    std::vector<cv::Point2f> unclipBox(cv::Point2f vertices[4], double area, float unclip_ratio, double perimeter) {
        // Simplified unclip using direct offset calculation
        double distance = area * unclip_ratio / perimeter;
        
        // Calculate center point
        cv::Point2f center(0, 0);
        for (int i = 0; i < 4; i++) {
            center.x += vertices[i].x;
            center.y += vertices[i].y;
        }
        center.x /= 4.0f;
        center.y /= 4.0f;
        
        // Expand each vertex away from center
        std::vector<cv::Point2f> expanded_box;
        for (int i = 0; i < 4; i++) {
            cv::Point2f direction = vertices[i] - center;
            float length = cv::norm(direction);
            if (length > 0) {
                direction /= length;
                cv::Point2f new_point = vertices[i] + direction * (float)distance;
                expanded_box.push_back(new_point);
            } else {
                expanded_box.push_back(vertices[i]);
            }
        }
        
        return expanded_box;
    }
};

class PaddleOCRRecognizer {
private:
    Ort::Session* session;
    Ort::Env env;
    Ort::SessionOptions session_options;
    std::vector<const char*> input_names;
    std::vector<const char*> output_names;
    std::vector<std::string> character_dict;
    
    // Pre-allocated buffers
    std::vector<float> input_buffer;
    cv::Mat resized_buffer;
    
public:
    PaddleOCRRecognizer(const std::string& model_path, const std::string& dict_path) 
        : env(ORT_LOGGING_LEVEL_ERROR, "PaddleOCRRecognizer") {
        
        // Configure session options for optimal performance
        session_options.SetIntraOpNumThreads(2);  // Reduced for better performance
        session_options.SetInterOpNumThreads(1);  // Sequential execution
        session_options.SetGraphOptimizationLevel(GraphOptimizationLevel::ORT_ENABLE_ALL);
        session_options.SetExecutionMode(ExecutionMode::ORT_SEQUENTIAL);
        session_options.EnableCpuMemArena();
        session_options.EnableMemPattern();
        
        session = new Ort::Session(env, model_path.c_str(), session_options);
        
        Ort::AllocatorWithDefaultOptions allocator;
        size_t num_input_nodes = session->GetInputCount();
        size_t num_output_nodes = session->GetOutputCount();
        
        for (size_t i = 0; i < num_input_nodes; i++) {
            auto input_name = session->GetInputNameAllocated(i, allocator);
            input_names.push_back(strdup(input_name.get()));
        }
        
        for (size_t i = 0; i < num_output_nodes; i++) {
            auto output_name = session->GetOutputNameAllocated(i, allocator);
            output_names.push_back(strdup(output_name.get()));
        }
        
        // Load character dictionary
        loadCharacterDict(dict_path);
        
        // Pre-allocate buffers
        input_buffer.resize(3 * 48 * 320);
        resized_buffer = cv::Mat(48, 320, CV_8UC3);
    }
    
    ~PaddleOCRRecognizer() {
        delete session;
    }
    
    void loadCharacterDict(const std::string& dict_path) {
        std::ifstream file(dict_path);
        std::string line;
        character_dict.reserve(6000);  // Pre-allocate
        // Don't add blank character here - index 0 is reserved for blank in CTC
        while (std::getline(file, line)) {
            if (!line.empty()) {
                character_dict.push_back(line);
            }
        }
    }
    
    std::pair<std::string, float> recognize(const cv::Mat& cropped_image) {
        // Calculate dynamic width based on aspect ratio like Python version
         int imgH = 48;  // Use working height
         int imgW = 320;
        float h = cropped_image.rows;
        float w = cropped_image.cols;
        float ratio = w / h;
        int resized_w = std::min(imgW, (int)std::ceil(imgH * ratio));
        
        // Resize image with dynamic width
        cv::Mat resized_image;
        cv::resize(cropped_image, resized_image, cv::Size(resized_w, imgH));
        
        // Convert to float and apply Python-style normalization: (pixel/255 - 0.5) / 0.5
        resized_image.convertTo(resized_image, CV_32F, 1.0 / 255.0);
        resized_image = (resized_image - 0.5) / 0.5;
        
        // Create padded image with zeros
        cv::Mat padded_image = cv::Mat::zeros(imgH, imgW, CV_32FC3);
        resized_image.copyTo(padded_image(cv::Rect(0, 0, resized_w, imgH)));
        
        // Convert from HWC to CHW format
        std::vector<cv::Mat> channels(3);
        cv::split(padded_image, channels);
        
        float* input_data = input_buffer.data();
        const int total_pixels = imgH * imgW;
        
        for (int c = 0; c < 3; ++c) {
            std::memcpy(input_data + c * total_pixels, channels[c].data, total_pixels * sizeof(float));
        }
        
        // Create input tensor
        std::vector<int64_t> input_shape = {1, 3, 48, 320};
        auto memory_info = Ort::MemoryInfo::CreateCpu(OrtArenaAllocator, OrtMemTypeDefault);
        Ort::Value input_tensor = Ort::Value::CreateTensor<float>(memory_info, input_data,
                                                                  input_buffer.size(), input_shape.data(), input_shape.size());
        
        // Run inference
        auto output_tensors = session->Run(Ort::RunOptions{nullptr}, input_names.data(), &input_tensor, 1, output_names.data(), output_names.size());
        
        // Optimized CTC decoding
        return decodeCTC(output_tensors[0]);
    }
    
private:
    std::pair<std::string, float> decodeCTC(Ort::Value& output_tensor) {
        float* output_data = output_tensor.GetTensorMutableData<float>();
        auto output_shape = output_tensor.GetTensorTypeAndShapeInfo().GetShape();
        
        int seq_len = output_shape[1];
        int num_classes = output_shape[2];
        
        std::string recognized_text;
        recognized_text.reserve(seq_len);  // Pre-allocate
        
        float total_score = 0.0f;
        int valid_chars = 0;
        int prev_char = -1;
        
        for (int t = 0; t < seq_len; t++) {
            // Vectorized max finding
            float* row_data = output_data + t * num_classes;
            int max_idx = std::max_element(row_data, row_data + num_classes) - row_data;
            float max_prob = row_data[max_idx];
            
            // CTC decoding: skip blank (0) and repeated characters
            if (max_idx != 0 && max_idx != prev_char && max_idx <= character_dict.size()) {
                recognized_text += character_dict[max_idx - 1];  // Adjust for 0-based blank
                total_score += max_prob;
                valid_chars++;
            }
            prev_char = max_idx;
        }
        
        float avg_score = valid_chars > 0 ? total_score / valid_chars : 0.0f;
        return std::make_pair(recognized_text, avg_score);
    }
};

struct OCRResult {
    std::string text;
    float confidence;
    std::vector<cv::Point2f> box;
};

class PaddleOCR {
private:
    PaddleOCRDetector* detector;
    PaddleOCRRecognizer* recognizer;
    
public:
    PaddleOCR(const std::string& det_model_path, const std::string& rec_model_path, const std::string& dict_path) {
        detector = new PaddleOCRDetector(det_model_path);
        recognizer = new PaddleOCRRecognizer(rec_model_path, dict_path);
    }
    
    ~PaddleOCR() {
        delete detector;
        delete recognizer;
    }
    
    std::vector<OCRResult> ocr_with_boxes(const cv::Mat& image) {
        // Detect text boxes
        auto text_boxes = detector->detect(image);
        
        std::vector<OCRResult> results;
        results.reserve(text_boxes.size());
        
        // Pre-crop all images first (this is fast)
        std::vector<cv::Mat> cropped_images;
        cropped_images.reserve(text_boxes.size());
        for (const auto& box : text_boxes) {
            cropped_images.push_back(getRotateCropImage(image, box));
        }
        
        // Process recognition in parallel using OpenMP
        std::vector<std::pair<std::string, float>> recognition_results(text_boxes.size());
        
        #pragma omp parallel for
        for (size_t i = 0; i < cropped_images.size(); ++i) {
            recognition_results[i] = recognizer->recognize(cropped_images[i]);
        }
        
        // Collect results with confidence filtering
        for (size_t i = 0; i < text_boxes.size(); ++i) {
            if (recognition_results[i].second >= 0.7f) {
                OCRResult result;
                result.text = recognition_results[i].first;
                result.confidence = recognition_results[i].second;
                result.box = text_boxes[i];
                results.push_back(result);
            }
        }
        
        return results;
    }
    
    // Optimized search function with early exit
    OCRResult search_text_fast(const cv::Mat& image, const std::string& target_text) {
        // Detect text boxes
        auto text_boxes = detector->detect(image);
        
        OCRResult best_match;
        bool found = false;
        float highest_confidence = 0.0f;
        
        // Process text boxes one by one with early exit
        for (size_t i = 0; i < text_boxes.size(); ++i) {
            cv::Mat cropped_image = getRotateCropImage(image, text_boxes[i]);
            auto recognition_result = recognizer->recognize(cropped_image);
            
            // Check if this matches our target text
            if (recognition_result.first == target_text && recognition_result.second >= 0.7f) {
                if (!found || recognition_result.second > highest_confidence) {
                    best_match.text = recognition_result.first;
                    best_match.confidence = recognition_result.second;
                    best_match.box = text_boxes[i];
                    highest_confidence = recognition_result.second;
                    found = true;
                    
                    // Early exit if we found a high-confidence match
                    if (recognition_result.second >= 0.95f) {
                        break;
                    }
                }
            }
        }
        
        return best_match;
    }
};

void print_help(const char* program_name) {
    // No help output
}

int main(int argc, char* argv[]) {
    // Redirect stderr to suppress warnings
    freopen("NUL", "w", stderr);
    
    if (argc < 2 || argc > 4) {
        return -1;
    }
    
    std::string image_path = argv[1];
    bool json_output = false;
    std::string search_text = "";
    
    // Parse arguments
    for (int i = 2; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--json") {
            json_output = true;
        } else if (arg == "--search" && i + 1 < argc) {
            search_text = argv[++i];
        } else {
            return -1;  // Invalid option
        }
    }
    
    // Load image
    cv::Mat image = cv::imread(image_path);
    if (image.empty()) {
        return -1;
    }
    
    // Initialize OCR
    PaddleOCR ocr("models/ch_PP-OCRv4_det_infer.onnx", "models/ch_PP-OCRv4_rec_infer.onnx", "models/ppocr_keys_v1.txt");
    
    // Measure processing time
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Perform OCR (only if not searching)
    std::vector<OCRResult> results;
    if (search_text.empty()) {
        results = ocr.ocr_with_boxes(image);
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // Handle search functionality with optimized fast search
        if (!search_text.empty()) {
            // Use fast search with early exit
            auto best_match = ocr.search_text_fast(image, search_text);
            
            if (!best_match.text.empty()) {
                // Calculate bounding box coordinates
                float min_x = best_match.box[0].x, max_x = best_match.box[0].x;
                float min_y = best_match.box[0].y, max_y = best_match.box[0].y;
                
                for (const auto& point : best_match.box) {
                    min_x = std::min(min_x, point.x);
                    max_x = std::max(max_x, point.x);
                    min_y = std::min(min_y, point.y);
                    max_y = std::max(max_y, point.y);
                }
                
                // Output simple JSON format
                std::cout << "{\"text\":\"" << best_match.text << "\",\"x\":" << std::fixed << std::setprecision(0) << min_x << ",\"y\":" << min_y << ",\"width\":" << (max_x - min_x) << ",\"height\":" << (max_y - min_y) << "}" << std::endl;
            }
            // If no match found, exit silently without output
            return 0;
        }
    
    // Normal OCR output when no search text provided
    std::vector<OCRResult> filtered_results = results;
    
    // Output results
    if (json_output) {
        std::cout << "{\"results\":[";
        for (size_t i = 0; i < filtered_results.size(); ++i) {
            if (i > 0) std::cout << ",";
            std::cout << "{\"text\":\"" << filtered_results[i].text << "\",";
            std::cout << "\"confidence\":" << std::fixed << std::setprecision(3) << filtered_results[i].confidence << ",";
            
            // Simplified coordinates: only top-left and bottom-right
            if (filtered_results[i].box.size() >= 4) {
                float min_x = filtered_results[i].box[0].x, max_x = filtered_results[i].box[0].x;
                float min_y = filtered_results[i].box[0].y, max_y = filtered_results[i].box[0].y;
                
                for (const auto& point : filtered_results[i].box) {
                    min_x = std::min(min_x, point.x);
                    max_x = std::max(max_x, point.x);
                    min_y = std::min(min_y, point.y);
                    max_y = std::max(max_y, point.y);
                }
                
                std::cout << "\"box\":[[" << std::fixed << std::setprecision(1) << min_x << "," << min_y << "],[" << max_x << "," << max_y << "]]";
            } else {
                std::cout << "\"box\":[]";
            }
            std::cout << "}";
        }
        std::cout << "],\"processing_time_ms\":" << duration.count() << ",\"search_text\":\"" << search_text << "\",\"found_count\":" << filtered_results.size() << "}" << std::endl;
    } else {
        for (size_t i = 0; i < filtered_results.size(); ++i) {
             std::cout << std::endl << (i + 1) << ". 文本: " << filtered_results[i].text << std::endl;
             std::cout << "   置信度: " << std::fixed << std::setprecision(3) << filtered_results[i].confidence << std::endl;
             
             // Display coordinates
             if (filtered_results[i].box.size() >= 4) {
                 float min_x = filtered_results[i].box[0].x, max_x = filtered_results[i].box[0].x;
                 float min_y = filtered_results[i].box[0].y, max_y = filtered_results[i].box[0].y;
                 
                 for (const auto& point : filtered_results[i].box) {
                     min_x = std::min(min_x, point.x);
                     max_x = std::max(max_x, point.x);
                     min_y = std::min(min_y, point.y);
                     max_y = std::max(max_y, point.y);
                 }
                 
                 std::cout << "   坐标: [" << std::fixed << std::setprecision(1) << min_x << "," << min_y << "] 到 [" << max_x << "," << max_y << "]" << std::endl;
                 std::cout << "   中心点: [" << (min_x + max_x) / 2 << "," << (min_y + max_y) / 2 << "]" << std::endl;
             }
         }
        
        std::cout << std::endl << "OCR识别完成！" << std::endl;
        std::cout << "Processing time: " << duration.count() << "ms" << std::endl;
    }
    
    return 0;
}

// Implement precise quadrilateral cropping like Python's get_rotate_crop_image
cv::Mat getRotateCropImage(const cv::Mat& img, const std::vector<cv::Point2f>& box) {
    // Calculate the width and height of the rotated rectangle
    float width1 = cv::norm(box[0] - box[1]);
    float width2 = cv::norm(box[2] - box[3]);
    float height1 = cv::norm(box[0] - box[3]);
    float height2 = cv::norm(box[1] - box[2]);
    
    int crop_width = std::max(width1, width2);
    int crop_height = std::max(height1, height2);
    
    // Ensure minimum dimensions
    crop_width = std::max(crop_width, 1);
    crop_height = std::max(crop_height, 1);
    
    // Define destination points for perspective transformation
    std::vector<cv::Point2f> dst_pts = {
        cv::Point2f(0, 0),
        cv::Point2f(crop_width, 0),
        cv::Point2f(crop_width, crop_height),
        cv::Point2f(0, crop_height)
    };
    
    // Get perspective transformation matrix
    cv::Mat M = cv::getPerspectiveTransform(box, dst_pts);
    
    // Apply perspective transformation
    cv::Mat cropped;
    cv::warpPerspective(img, cropped, M, cv::Size(crop_width, crop_height), cv::INTER_CUBIC);
    
    return cropped;
}