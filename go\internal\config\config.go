package config

import (
	"os"
	"path/filepath"
)

// 应用配置常量
const (
	DefaultPort      = "9088"
	ScreenshotDir    = "screenshots"
	PaddleOCRTool    = "./paddleocr_tool"
	U2JarPath        = "u2.jar"
	LibPath          = "./lib:$LD_LIBRARY_PATH"
	TimestampFormat  = "20060102_150405"
	ScreenshotPrefix = "screenshot_"
	ScreenshotExt    = ".png"
)

// AppConfig 应用配置结构
type AppConfig struct {
	Port          string
	ScreenshotDir string
	PaddleOCRTool string
	U2JarPath     string
	LibPath       string
}

// NewAppConfig 创建新的应用配置
func NewAppConfig() *AppConfig {
	return &AppConfig{
		Port:          getPort(),
		ScreenshotDir: ScreenshotDir,
		PaddleOCRTool: PaddleOCRTool,
		U2JarPath:     U2JarPath,
		LibPath:       LibPath,
	}
}

// getPort 获取端口号
func getPort() string {
	if port := os.Getenv("PORT"); port != "" {
		return port
	}
	return DefaultPort
}

// InitializeDirectories 初始化必要的目录
func (c *AppConfig) InitializeDirectories() error {
	// 获取程序执行目录并切换
	if execPath, err := os.Executable(); err == nil {
		programDir := filepath.Dir(execPath)
		if err := os.Chdir(programDir); err != nil {
			return err
		}
	}

	// 创建截图目录
	if err := os.MkdirAll(c.ScreenshotDir, 0755); err != nil {
		return err
	}

	return nil
}
