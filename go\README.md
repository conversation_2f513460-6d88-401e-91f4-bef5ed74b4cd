# OCR自动化系统 - 性能优化版本

## 概述

这是一个针对Android设备的OCR自动化系统，经过性能优化，大幅提升了OCR识别和UI自动化的响应速度。

## 性能优化特性

### 🚀 OCR性能优化
- **常驻进程模式**: OCR服务以daemon模式运行，避免重复加载模型
- **模型缓存**: PaddleOCR模型保持在内存中，减少初始化时间
- **智能回退**: 如果常驻进程失败，自动回退到传统模式
- **性能提升**: OCR识别速度提升约60-80%

### 🔗 UI自动化优化
- **连接池**: HTTP客户端使用连接池，减少连接开销
- **会话复用**: 复用与UIAutomator2服务的连接
- **智能缓存**: UI层级信息缓存2秒，减少重复获取
- **自动管理**: 自动启动和监控U2服务状态

### 📱 移动端界面
- **响应式设计**: 完全适配移动设备
- **触摸优化**: 针对触摸操作优化的界面元素
- **实时状态**: 显示真实的设备信息和服务状态
- **任务管理**: 支持拖拽排序和批量操作

## 快速开始

### 编译和运行

```bash
# 编译
go build -o ocr_server_optimized main.go

# 运行
./ocr_server_optimized

# 或使用启动脚本
chmod +x start.sh
./start.sh
```

### 访问界面

- **移动端界面**: http://localhost:8080/mobile
- **性能监控**: http://localhost:8080/performance
- **健康检查**: http://localhost:8080/health

## API接口

### OCR相关
- `POST /ocr` - OCR文字识别
- `POST /ocr-click` - OCR识别并点击

### UI自动化
- `POST /ui/start-u2` - 启动U2服务
- `POST /ui/stop-u2` - 停止U2服务
- `GET /ui/service-status` - 获取服务状态
- `GET /ui/dump` - 获取UI层级

### 基础操作
- `POST /screenshot` - 截图
- `POST /click` - 点击
- `POST /swipe` - 滑动

### 设备信息
- `GET /device/info` - 获取设备信息

## 性能对比

| 功能 | 优化前 | 优化后 | 提升 |
|------|--------|--------|------|
| OCR识别 | 2-3秒 | 0.5-1秒 | 60-80% |
| UI操作 | 1-2秒 | 0.2-0.5秒 | 70-80% |
| 服务启动 | 5-10秒 | 2-3秒 | 50-70% |

## 技术架构

### 集成服务
- `IntegratedOCRService`: 集成OCR服务，支持常驻进程
- `IntegratedUIService`: 集成UI服务，支持连接池

### 优化策略
1. **进程复用**: 避免重复启动外部进程
2. **连接池**: 复用HTTP连接
3. **智能缓存**: 缓存频繁访问的数据
4. **异步处理**: 非阻塞的服务管理

## 配置说明

系统会自动检测和配置以下组件：
- PaddleOCR工具路径
- UIAutomator2 JAR文件
- 截图目录
- 模型文件路径

## 故障排除

### OCR常驻进程启动失败
- 检查PaddleOCR工具是否支持`--daemon`参数
- 确认模型文件路径正确
- 查看日志中的错误信息

### UI服务连接失败
- 确认设备已连接并启用USB调试
- 检查UIAutomator2服务是否正常运行
- 验证端口9008是否可访问

## 开发说明

### 项目结构
```
go/
├── main.go                          # 主程序入口
├── internal/
│   ├── services/
│   │   ├── integrated_ocr_service.go    # 集成OCR服务
│   │   ├── integrated_ui_service.go     # 集成UI服务
│   │   └── script_service.go            # 脚本服务
│   ├── handlers/                    # HTTP处理器
│   ├── models/                      # 数据模型
│   └── utils/                       # 工具函数
└── templates/
    └── mobile.html                  # 移动端界面
```

### 扩展开发
- 添加新的OCR引擎支持
- 扩展UI自动化功能
- 增加更多设备信息获取
- 优化缓存策略

## 许可证

本项目仅供学习和研究使用。
