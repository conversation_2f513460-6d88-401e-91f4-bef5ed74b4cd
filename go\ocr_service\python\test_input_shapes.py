#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import onnxruntime as ort
import numpy as np
import cv2
from pathlib import Path

def test_model_input_shapes():
    """测试ONNX模型对不同输入形状的支持"""
    
    model_path = "libs/models/ch_PP-OCRv4_rec_infer.onnx"
    
    if not Path(model_path).exists():
        print(f"模型文件不存在: {model_path}")
        return
    
    # 创建ONNX Runtime会话
    session = ort.InferenceSession(model_path)
    
    # 获取输入信息
    input_info = session.get_inputs()[0]
    print(f"模型输入名称: {input_info.name}")
    print(f"模型输入形状: {input_info.shape}")
    print(f"模型输入类型: {input_info.type}")
    
    # 测试不同的输入尺寸
    test_shapes = [
        (1, 3, 32, 32),   # 正方形
        (1, 3, 32, 64),   # 2:1比例
        (1, 3, 32, 88),   # 之前失败的尺寸
        (1, 3, 32, 96),   # 3:1比例
        (1, 3, 32, 128),  # 4:1比例
        (1, 3, 32, 192),  # 6:1比例
    ]
    
    for shape in test_shapes:
        try:
            # 创建随机输入数据
            input_data = np.random.randn(*shape).astype(np.float32)
            
            # 运行推理
            output = session.run(None, {input_info.name: input_data})
            
            print(f"✓ 形状 {shape} 测试成功，输出形状: {[o.shape for o in output]}")
            
        except Exception as e:
            print(f"✗ 形状 {shape} 测试失败: {str(e)}")

if __name__ == "__main__":
    test_model_input_shapes()