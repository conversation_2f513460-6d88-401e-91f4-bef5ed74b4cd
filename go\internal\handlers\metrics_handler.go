package handlers

import (
	"net/http"

	"ocr-server/internal/services"

	"github.com/gin-gonic/gin"
)

// MetricsHandler 性能指标处理器
type MetricsHandler struct {
	ocrService *services.IntegratedOCRService
}

// NewMetricsHandler 创建新的性能指标处理器
func NewMetricsHandler(ocrService *services.IntegratedOCRService) *MetricsHandler {
	return &MetricsHandler{
		ocrService: ocrService,
	}
}

// HandleMetrics 处理性能指标请求
func (h *MetricsHandler) HandleMetrics(c *gin.Context) {
	metrics := h.ocrService.GetMetrics()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"metrics": metrics,
	})
}

// HandleHealthCheck 处理健康检查请求
func (h *MetricsHandler) HandleHealthCheck(c *gin.Context) {
	metrics := h.ocrService.GetMetrics()

	// 简单的健康检查逻辑
	isHealthy := true
	status := "healthy"

	// 如果失败率超过50%，认为不健康
	if metrics.TotalRequests > 0 {
		failureRate := float64(metrics.FailedRequests) / float64(metrics.TotalRequests)
		if failureRate > 0.5 {
			isHealthy = false
			status = "unhealthy"
		}
	}

	// 传统模式不需要检查重启次数

	httpStatus := http.StatusOK
	if !isHealthy {
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, gin.H{
		"success": isHealthy,
		"status":  status,
		"metrics": metrics,
	})
}
