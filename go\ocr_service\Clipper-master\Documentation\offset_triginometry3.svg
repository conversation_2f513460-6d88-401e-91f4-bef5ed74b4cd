<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="800"
   height="600"
   id="svg2"
   version="1.1"
   inkscape:version="0.48.2 r9819"
   sodipodi:docname="offset_triginometry3.svg">
  <defs
     id="defs4" />
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="0.64"
     inkscape:cx="-24.27"
     inkscape:cy="218.1"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:window-width="1280"
     inkscape:window-height="753"
     inkscape:window-x="-4"
     inkscape:window-y="-4"
     inkscape:window-maximized="1"
     showborder="true"
     inkscape:showpageshadow="true"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0">
    <inkscape:grid
       type="xygrid"
       id="grid3779"
       empspacing="5"
       visible="true"
       enabled="true"
       snapvisiblegridlinesonly="true" />
  </sodipodi:namedview>
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Layer 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-407.6,90.95)">
    <path
       style="color:#000000;fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:4, 4;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       d="m 604.8,209.6 85.8,-55.7"
       id="path3907"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="cc" />
    <path
       style="color:#000000;fill:none;stroke:#000000;stroke-width:1.8;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       d="M 420.4,326.1 603.7,10.08 757.6,263.6"
       id="path2985"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccc" />
    <text
       xml:space="preserve"
       style="font-size:20px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="624.09735"
       y="215.06464"
       id="text3861"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3863"
         x="624.09735"
         y="215.06464"
         style="font-weight:bold;-inkscape-font-specification:Tahoma Bold">ß</tspan></text>
    <path
       sodipodi:nodetypes="cccccccc"
       inkscape:connector-curvature="0"
       id="path3857-2"
       d="m 603,29.9 15,15.8 -10,0 0,155.1 -10,0 0,-155.1 -10,0 z"
       style="color:#000000;fill:#ffff00;fill-opacity:1;stroke:#000000;stroke-width:1.776;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       inkscape:transform-center-x="-20.96"
       inkscape:transform-center-y="-1.652" />
    <text
       xml:space="preserve"
       style="font-size:10px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="682.20001"
       y="261.375"
       id="text3952"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3954"
         x="682.20001"
         y="261.375"
         style="font-size:14px">delta</tspan></text>
    <text
       xml:space="preserve"
       style="font-size:10px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="698.20001"
       y="151.875"
       id="text3977"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3979"
         x="698.20001"
         y="151.875"
         style="font-size:14px">c</tspan></text>
    <text
       xml:space="preserve"
       style="font-size:10px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="599.76099"
       y="3.3956299"
       id="text3977-5-1-2"
       sodipodi:linespacing="125%"
       inkscape:transform-center-x="-52.52"
       inkscape:transform-center-y="54.68"><tspan
         sodipodi:role="line"
         id="tspan3979-1-5-7"
         x="599.76099"
         y="3.3956299"
         style="font-size:14px">b</tspan></text>
    <text
       xml:space="preserve"
       style="font-size:14px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="766"
       y="-49.349998"
       id="text4077"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         x="766"
         y="-49.349998"
         id="tspan4087">When 'mitering' offset polygons, the maximum distance </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="-31.849998"
         id="tspan3063">point 'b' can be from point 'a' is set by 'limit' where limit </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="-14.349998"
         id="tspan3065">is a multiple of delta. Therefore, for any given angle</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="3.1500015"
         id="tspan3067">we need to know if length(ab) &gt; limit * delta.</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="20.650002"
         id="tspan4083" /><tspan
         sodipodi:role="line"
         x="766"
         y="38.150002"
         id="tspan3890">Find the largest angle ß (or smallest Ø since Ø = pi - ß) </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="55.650002"
         id="tspan3904">for a given limit, expressing ß as sin(ß) or cos(ß) since</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="73.150002"
         id="tspan3908">these can easily be derived from cross or dot products </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="90.650002"
         id="tspan3916">respectively.</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="98.598343"
         id="tspan3884"
         style="font-size:4px"> </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="113.15"
         id="tspan3888">angle(abc) = Ø/2 </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="130.64999"
         id="tspan3083">length(ab) = limit * delta</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="148.14999"
         id="tspan3087">length(ac) = delta</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="165.64999"
         id="tspan3089">sin(Ø/2) = delta / (limit * delta) = 1 / limit</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="183.14999"
         id="tspan3157">Given that sin(Ø/2) = sqrt((1-cos(Ø))/2) **</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="200.64999"
         id="tspan3151">1 / limit = sqrt((1-cos(Ø))/2)</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="218.14999"
         id="tspan3161">limit = sqrt(2 / (1-cos(Ø)))</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="235.64999"
         id="tspan3815"
         style="font-weight:normal">1-cos(Ø) = 2 / sqr(limit) </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="253.14999"
         id="tspan3043">Since Ø = pi - ß ...</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="270.64999"
         id="tspan3868">1-cos(pi - ß) = 2 / sqr(limit)</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="288.14999"
         id="tspan3047">and given cos(pi-ß) = -cos(ß) ** ... </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="296.09836"
         style="font-size:4px;font-weight:normal"
         id="tspan3823"> </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="310.64999"
         style="font-weight:bold"
         id="tspan3122">1+cos(ß) = 2 / sqr(limit) </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="318.59836"
         style="font-size:4px;font-weight:normal"
         id="tspan3118"> </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="333.14999"
         style="font-weight:normal"
         id="tspan3114">cos(ß) = 2 / sqr(limit) - 1</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="350.64999"
         style="font-weight:bold"
         id="tspan3051" /><tspan
         sodipodi:role="line"
         x="766"
         y="368.14999"
         style="font-weight:normal"
         id="tspan3066">Example: if miter limit = 2 (ie 2 times delta) then </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="385.64999"
         style="font-weight:normal"
         id="tspan3070">cos(ß) = (2 / 4) -1 = -0.5 and so ß = 120 degrees. </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="403.14999"
         style="font-weight:normal"
         id="tspan3072">Therefore, when ß &gt; 120 deg. (or Ø &lt; 60 deg.), the</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="420.64999"
         style="font-weight:normal"
         id="tspan3090">distance point 'b' would be from point 'a' would exceed </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="438.14999"
         style="font-weight:normal"
         id="tspan3094">the limit.</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="446.09836"
         id="tspan3171"
         style="font-size:4px;font-weight:bold">   </tspan><tspan
         sodipodi:role="line"
         x="766"
         y="451.09836"
         style="font-size:4px;font-weight:bold"
         id="tspan3049" /><tspan
         sodipodi:role="line"
         x="766"
         y="462.78452"
         id="tspan3163"
         style="font-size:11px">** see http://en.wikipedia.org/wiki/List_of_trigonometric_identities</tspan><tspan
         sodipodi:role="line"
         x="766"
         y="476.53452"
         id="tspan3153" /><tspan
         sodipodi:role="line"
         x="766"
         y="490.28452"
         id="tspan3145" /><tspan
         sodipodi:role="line"
         x="766"
         y="504.03452"
         id="tspan4124"
         style="font-weight:bold" /><tspan
         sodipodi:role="line"
         x="766"
         y="517.78448"
         id="tspan4116" /><tspan
         sodipodi:role="line"
         x="766"
         y="531.53448"
         id="tspan4095" /></text>
    <path
       style="color:#000000;fill:none;stroke:#000000;stroke-width:1;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:2, 2;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       d="m 684.7,144.1 -10.6,5.9 5.4,10.1"
       id="path4091"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccc" />
    <text
       xml:space="preserve"
       style="font-size:10px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="588.19745"
       y="208.67169"
       id="text3977-1"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3979-7"
         x="588.19745"
         y="208.67169"
         style="font-size:14px">a</tspan></text>
    <g
       id="g3841"
       transform="translate(400,-20.7)">
      <path
         sodipodi:nodetypes="cc"
         inkscape:connector-curvature="0"
         id="path3026"
         d="M 175.6,276.8 233.7,175.7"
         style="color:#000000;fill:none;stroke:#000000;stroke-width:1.8;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:7.2, 7.2;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
      <path
         sodipodi:nodetypes="cc"
         inkscape:connector-curvature="0"
         id="path3809"
         d="m 227.7,188.7 c 27.7,17.9 31.6,57.6 -0.5,76.7"
         style="color:#000000;fill:none;stroke:#000000;stroke-width:1.81;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:7.24, 7.24;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate" />
    </g>
    <path
       style="color:#000000;fill:#00ff00;fill-opacity:1;stroke:#000000;stroke-width:2.1383;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       d="m 724.8,212.4 -9.2,24.2 -5.6,-8.3 -46.8,31.5 5.4,8.3 -26,-0.8 9.4,-24.3 5.5,8.3 47,-31.3 -5.6,-8.3 z"
       id="path3777-9"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccccccccccc"
       inkscape:transform-center-x="-96.45"
       inkscape:transform-center-y="-137.3" />
    <text
       xml:space="preserve"
       style="font-size:14px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       x="595.66949"
       y="246.41019"
       id="text3845"
       sodipodi:linespacing="125%"><tspan
         sodipodi:role="line"
         id="tspan3847"
         x="595.66949"
         y="246.41019"
         style="font-size:20px;font-weight:bold;-inkscape-font-specification:Tahoma Bold">Ø</tspan></text>
    <text
       sodipodi:linespacing="125%"
       id="text3849"
       y="246.41019"
       x="595.66949"
       style="font-size:14px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-indent:0;text-align:start;text-decoration:none;line-height:125%;letter-spacing:0px;word-spacing:0px;text-transform:none;direction:ltr;block-progression:tb;writing-mode:lr-tb;text-anchor:start;baseline-shift:baseline;color:#000000;fill:#000000;fill-opacity:1;fill-rule:nonzero;stroke:none;stroke-width:1px;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate;font-family:Tahoma;-inkscape-font-specification:Tahoma"
       xml:space="preserve"><tspan
         style="font-size:20px;font-weight:bold;-inkscape-font-specification:Tahoma Bold"
         y="246.41019"
         x="595.66949"
         id="tspan3851"
         sodipodi:role="line">Ø</tspan></text>
    <path
       style="color:#000000;fill:none;stroke:#000000;stroke-width:3.6;stroke-linecap:butt;stroke-linejoin:miter;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;stroke-dashoffset:0;marker:none;visibility:visible;display:inline;overflow:visible;enable-background:accumulate"
       d="M 530.8,333.3 603,210 677.5,330.2"
       id="path2985-1-1"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="ccc" />
  </g>
</svg>
