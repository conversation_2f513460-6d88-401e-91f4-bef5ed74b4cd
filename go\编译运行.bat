@echo off
chcp 936 >nul
setlocal enabledelayedexpansion

echo ========================================
echo OCR Automation Project ARM64 Build and Deploy Script
echo Optimized Version - Modular Architecture
echo ========================================
echo.

echo [0/5] Checking build environment...
where go >nul 2>&1
if errorlevel 1 (
    echo Error: Go compiler not found! Please install Go language environment first
    pause
    exit /b 1
)
echo Go environment check passed

echo Checking project files...
if not exist "main.go" (
    echo Error: main.go file not found!
    pause
    exit /b 1
)
if not exist "internal" (
    echo Error: internal directory not found! Please ensure code refactoring is complete
    pause
    exit /b 1
)
echo Project files check passed

echo [1/5] Setting environment variables...
set GOOS=android
set GOARCH=arm64
set CGO_ENABLED=0

echo [2/5] Building ARM64 version...
echo Compiling optimized main program...
go build -o main_android_arm64 main.go
if errorlevel 1 (
    echo Main program compilation failed!
    pause
    exit /b 1
)
echo Compilation successful!

echo [3/5] Checking device connection...
adb devices >nul 2>&1
if errorlevel 1 (
    echo ADB not found! Please ensure Android SDK is installed and added to PATH
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('adb devices ^| find "device" ^| find /v "List"') do (
    set device_found=1
)
if not defined device_found (
    echo No connected devices found! Please ensure device is connected and USB debugging is enabled
    pause
    exit /b 1
)
echo Device connection OK

echo [4/5] Pushing files to device...
echo Pushing main program...
adb push main_android_arm64 /data/local/tmp/
if errorlevel 1 (
    echo Main program push failed! Please check device permissions
    pause
    exit /b 1
)
echo Pushing OCR tool...
adb push ocr_service\libs\arm64-v8a\paddleocr_tool /data/local/tmp/
if errorlevel 1 (
    echo OCR tool push failed! Please check device permissions
    pause
    exit /b 1
)
echo Pushing OCR models...
adb push models /data/local/tmp/ 2>nul
echo Pushing OCR libraries...
adb shell mkdir -p /data/local/tmp/lib
adb push ocr_service\libs\arm64-v8a\libonnxruntime.so /data/local/tmp/lib/
echo Pushing U2 jar file...
adb push uiauto\u2.jar /data/local/tmp/
if errorlevel 1 (
    echo U2 jar push failed! Please check device permissions
    pause
    exit /b 1
)
echo Pushing template files...
adb push templates /data/local/tmp/ 2>nul
echo File push completed!

echo [5/5] Setting permissions and testing...
adb shell chmod 755 /data/local/tmp/main_android_arm64
adb shell chmod 755 /data/local/tmp/paddleocr_tool
adb shell mkdir -p /data/local/tmp/screenshots
echo Starting test run...
adb shell "cd /data/local/tmp && ./main_android_arm64 2>&1 || echo 'Program test completed'"

echo.
echo ========================================
echo Deploy and test completed!
echo Manual run commands:
echo adb shell
echo cd /data/local/tmp
echo ./main_android_arm64
echo.
echo Optimized architecture features:
echo - Modular code structure
echo - Unified error handling
echo - Performance optimization cache
echo - Comprehensive logging
echo ========================================

