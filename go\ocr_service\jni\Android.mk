LOCAL_PATH := $(call my-dir)

# 预编译的OpenCV静态库
include $(CLEAR_VARS)
LOCAL_MODULE := opencv_core
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_core.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := opencv_imgproc
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_imgproc.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := opencv_highgui
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_highgui.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := opencv_features2d
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_features2d.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := opencv_photo
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_photo.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := opencv_video
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/staticlibs/$(TARGET_ARCH_ABI)/libopencv_video.a
include $(PREBUILT_STATIC_LIBRARY)

# 第三方库
include $(CLEAR_VARS)
LOCAL_MODULE := kleidicv
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/3rdparty/libs/$(TARGET_ARCH_ABI)/libkleidicv.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := kleidicv_hal
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/3rdparty/libs/$(TARGET_ARCH_ABI)/libkleidicv_hal.a
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE := kleidicv_thread
LOCAL_SRC_FILES := ../libs/opencv-mobile-4.11.0-android/sdk/native/3rdparty/libs/$(TARGET_ARCH_ABI)/libkleidicv_thread.a
include $(PREBUILT_STATIC_LIBRARY)

# ONNX Runtime预编译共享库
include $(CLEAR_VARS)
LOCAL_MODULE := onnxruntime
LOCAL_SRC_FILES := lib/$(TARGET_ARCH_ABI)/libonnxruntime.so
include $(PREBUILT_SHARED_LIBRARY)

# OCR优化版本命令行工具
include $(CLEAR_VARS)

LOCAL_MODULE := paddleocr_tool
LOCAL_SRC_FILES := paddleocr_onnx.cpp clipper.cpp
LOCAL_C_INCLUDES += $(LOCAL_PATH)/../
LOCAL_C_INCLUDES += $(LOCAL_PATH)/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include
LOCAL_C_INCLUDES += $(LOCAL_PATH)/include/onnxruntime

LOCAL_CPPFLAGS := -std=c++17 -frtti -fexceptions -O3 -DNDEBUG -march=armv8-a -mtune=cortex-a76
LOCAL_LDLIBS := -llog
LOCAL_LDFLAGS :=

LOCAL_STATIC_LIBRARIES := opencv_imgproc opencv_highgui opencv_features2d opencv_photo opencv_video opencv_core kleidicv kleidicv_hal kleidicv_thread
LOCAL_SHARED_LIBRARIES := onnxruntime

# Add OpenMP static library
LOCAL_LDLIBS += -L$(NDK_ROOT)/toolchains/llvm/prebuilt/windows-x86_64/lib/clang/17/lib/linux/aarch64 -lomp

include $(BUILD_EXECUTABLE)