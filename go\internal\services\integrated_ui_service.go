package services

import (
	"bytes"
	"crypto/md5"
	"encoding/json"
	"encoding/xml"
	"fmt"
	"math"
	"net/http"
	"os/exec"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"ocr-server/internal/config"
	"ocr-server/internal/models"
	"ocr-server/internal/utils"
)

// IntegratedUIService 集成的UI服务，使用连接池和会话复用
type IntegratedUIService struct {
	config       *config.AppConfig
	inputManager *utils.InputManager

	// U2服务管理
	u2ServiceCmd *exec.Cmd
	u2Running    bool
	u2Mutex      sync.RWMutex

	// HTTP客户端池
	httpClient *http.Client

	// 缓存
	hierarchyCache string
	cacheTime      time.Time
	cacheMutex     sync.RWMutex
	cacheTimeout   time.Duration

	// UI匹配器（集成uiauto功能）
	ignoreAttrs  map[string]bool
	screenWidth  int
	screenHeight int
}

// JSONRPCRequest JSON-RPC请求结构
type JSONRPCRequest struct {
	JSONRPC string        `json:"jsonrpc"`
	Method  string        `json:"method"`
	Params  []interface{} `json:"params"`
	ID      int           `json:"id"`
}

// JSONRPCResponse JSON-RPC响应结构
type JSONRPCResponse struct {
	JSONRPC string      `json:"jsonrpc"`
	Result  interface{} `json:"result"`
	Error   interface{} `json:"error"`
	ID      int         `json:"id"`
}

// UI元素相关结构体（从uiauto.go集成）
type Node struct {
	XMLName       xml.Name `xml:"node"`
	Index         string   `xml:"index,attr"`
	Text          string   `xml:"text,attr"`
	ResourceID    string   `xml:"resource-id,attr"`
	Class         string   `xml:"class,attr"`
	Package       string   `xml:"package,attr"`
	ContentDesc   string   `xml:"content-desc,attr"`
	Checkable     string   `xml:"checkable,attr"`
	Checked       string   `xml:"checked,attr"`
	Clickable     string   `xml:"clickable,attr"`
	Enabled       string   `xml:"enabled,attr"`
	Focusable     string   `xml:"focusable,attr"`
	Focused       string   `xml:"focused,attr"`
	Scrollable    string   `xml:"scrollable,attr"`
	LongClickable string   `xml:"long-clickable,attr"`
	Password      string   `xml:"password,attr"`
	Selected      string   `xml:"selected,attr"`
	Bounds        string   `xml:"bounds,attr"`
	Children      []Node   `xml:"node"`
}

type Hierarchy struct {
	XMLName xml.Name `xml:"hierarchy"`
	Nodes   []Node   `xml:"node"`
}

type ElementInfo struct {
	Index         string `json:"index"`
	Text          string `json:"text"`
	ResourceID    string `json:"resource_id"`
	Class         string `json:"class"`
	Package       string `json:"package"`
	ContentDesc   string `json:"content_desc"`
	Checkable     bool   `json:"checkable"`
	Checked       bool   `json:"checked"`
	Clickable     bool   `json:"clickable"`
	Enabled       bool   `json:"enabled"`
	Focusable     bool   `json:"focusable"`
	Focused       bool   `json:"focused"`
	Scrollable    bool   `json:"scrollable"`
	LongClickable bool   `json:"long_clickable"`
	Password      bool   `json:"password"`
	Selected      bool   `json:"selected"`
	Bounds        string `json:"bounds"`
	X             int    `json:"x"`
	Y             int    `json:"y"`
	Width         int    `json:"width"`
	Height        int    `json:"height"`
}

type MatchResult struct {
	Found       bool        `json:"found"`
	Element     ElementInfo `json:"element,omitempty"`
	Confidence  float64     `json:"confidence,omitempty"`
	MatchReason string      `json:"match_reason,omitempty"`
}

// NewIntegratedUIService 创建新的集成UI服务
func NewIntegratedUIService(cfg *config.AppConfig) *IntegratedUIService {
	service := &IntegratedUIService{
		config:       cfg,
		inputManager: utils.NewInputManager(),
		httpClient: &http.Client{
			Timeout: 10 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        10,
				MaxIdleConnsPerHost: 5,
				IdleConnTimeout:     30 * time.Second,
			},
		},
		cacheTimeout: 2 * time.Second, // 缓存2秒
		// 初始化UI匹配器
		ignoreAttrs: map[string]bool{
			"drawing-order": true,
			"display-id":    true,
			"index":         true,
		},
		screenWidth:  1080,
		screenHeight: 1920,
	}

	// 更新屏幕信息
	service.updateScreenInfo()

	// 自动启动U2服务
	go service.autoStartU2Service()

	return service
}

// autoStartU2Service 自动启动U2服务
func (ui *IntegratedUIService) autoStartU2Service() {
	// 等待一下再启动
	time.Sleep(1 * time.Second)

	if !ui.isU2ServiceRunning() {
		ui.StartU2Service()
	}
}

// StartU2Service 启动U2服务
func (ui *IntegratedUIService) StartU2Service() (*models.U2ServiceResponse, error) {
	ui.u2Mutex.Lock()
	defer ui.u2Mutex.Unlock()

	// 检查是否已经运行
	if ui.u2Running && ui.isU2ServiceRunning() {
		return &models.U2ServiceResponse{
			Success: true,
			Message: "U2服务已在运行",
		}, nil
	}

	// 停止现有服务
	if ui.u2ServiceCmd != nil {
		ui.u2ServiceCmd.Process.Kill()
		ui.u2ServiceCmd.Wait()
		ui.u2ServiceCmd = nil
	}

	// 启动新服务
	cmd := exec.Command("app_process", "-Djava.class.path="+ui.config.U2JarPath, "/system/bin", "com.wetest.uia2.Main")
	if err := cmd.Start(); err != nil {
		return &models.U2ServiceResponse{
			Success: false,
			Error:   fmt.Sprintf("启动U2服务失败: %v", err),
		}, nil
	}

	ui.u2ServiceCmd = cmd
	ui.u2Running = true

	// 等待服务就绪
	for i := 0; i < 50; i++ { // 最多等待5秒
		if ui.isU2ServiceRunning() {
			return &models.U2ServiceResponse{
				Success: true,
				Message: "U2服务启动成功",
			}, nil
		}
		time.Sleep(100 * time.Millisecond)
	}

	return &models.U2ServiceResponse{
		Success: false,
		Error:   "U2服务启动超时",
	}, nil
}

// StopU2Service 停止U2服务
func (ui *IntegratedUIService) StopU2Service() (*models.U2ServiceResponse, error) {
	ui.u2Mutex.Lock()
	defer ui.u2Mutex.Unlock()

	// 尝试通过HTTP请求停止服务
	req := JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  "stop",
		Params:  []interface{}{},
		ID:      1,
	}
	reqData, _ := json.Marshal(req)
	resp, err := ui.httpClient.Post("http://127.0.0.1:9008/jsonrpc/0", "application/json", bytes.NewBuffer(reqData))
	if err == nil {
		resp.Body.Close()
	}

	// 强制停止进程
	if ui.u2ServiceCmd != nil {
		if ui.u2ServiceCmd.Process != nil {
			ui.u2ServiceCmd.Process.Kill()
		}
		ui.u2ServiceCmd.Wait()
		ui.u2ServiceCmd = nil
	}

	// 使用系统命令停止
	exec.Command("killall", "app_process").Run()
	exec.Command("pkill", "-f", "com.wetest.uia2.Main").Run()

	ui.u2Running = false

	return &models.U2ServiceResponse{
		Success: true,
		Message: "U2服务已停止",
	}, nil
}

// GetU2ServiceStatus 获取U2服务状态
func (ui *IntegratedUIService) GetU2ServiceStatus() (*models.U2ServiceResponse, error) {
	if ui.isU2ServiceRunning() {
		return &models.U2ServiceResponse{
			Success: true,
			Message: "U2服务正在运行",
		}, nil
	} else {
		return &models.U2ServiceResponse{
			Success: true,
			Message: "U2服务未运行",
		}, nil
	}
}

// isU2ServiceRunning 检查U2服务是否运行
func (ui *IntegratedUIService) isU2ServiceRunning() bool {
	req := JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  "deviceInfo",
		Params:  []interface{}{},
		ID:      1,
	}

	reqData, _ := json.Marshal(req)
	resp, err := ui.httpClient.Post("http://127.0.0.1:9008/jsonrpc/0", "application/json", bytes.NewBuffer(reqData))
	if err != nil {
		return false
	}
	defer resp.Body.Close()

	var response JSONRPCResponse
	return json.NewDecoder(resp.Body).Decode(&response) == nil
}

// GetUIHierarchy 获取UI层级结构（带缓存）
func (ui *IntegratedUIService) GetUIHierarchy() (*models.UIMatchResponse, error) {
	// 检查缓存
	ui.cacheMutex.RLock()
	if ui.hierarchyCache != "" && time.Since(ui.cacheTime) < ui.cacheTimeout {
		cache := ui.hierarchyCache
		ui.cacheMutex.RUnlock()
		return &models.UIMatchResponse{
			Success: true,
			Result:  cache,
		}, nil
	}
	ui.cacheMutex.RUnlock()

	// 确保U2服务运行
	if !ui.isU2ServiceRunning() {
		ui.StartU2Service()
		time.Sleep(500 * time.Millisecond) // 等待服务启动
	}

	// 使用基础方法获取UI层级
	req := JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  "dumpWindowHierarchy",
		Params:  []interface{}{false}, // 使用false参数
		ID:      1,
	}

	reqData, _ := json.Marshal(req)
	resp, err := ui.httpClient.Post("http://127.0.0.1:9008/jsonrpc/0", "application/json", bytes.NewBuffer(reqData))
	if err != nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   fmt.Sprintf("获取UI层级失败: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	var response JSONRPCResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "解析响应失败",
		}, nil
	}

	if result, ok := response.Result.(string); ok && result != "" {
		// 更新缓存
		ui.cacheMutex.Lock()
		ui.hierarchyCache = result
		ui.cacheTime = time.Now()
		ui.cacheMutex.Unlock()

		elementCount := strings.Count(result, "<node")
		fmt.Printf("获取到UI层级，包含%d个<node>元素\n", elementCount)

		return &models.UIMatchResponse{
			Success: true,
			Result:  result,
		}, nil
	}

	return &models.UIMatchResponse{
		Success: false,
		Error:   "无法获取UI层级",
	}, nil
}

// ClearCache 清理缓存
func (ui *IntegratedUIService) ClearCache() {
	ui.cacheMutex.Lock()
	defer ui.cacheMutex.Unlock()
	ui.hierarchyCache = ""
}

// GenerateUIFingerprint 生成UI指纹
func (ui *IntegratedUIService) GenerateUIFingerprint(x, y int) (*models.UIFingerprintResponse, error) {
	// 确保U2服务运行
	if !ui.isU2ServiceRunning() {
		ui.StartU2Service()
		time.Sleep(500 * time.Millisecond)
	}

	// 先获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIFingerprintResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 查找指定坐标处的UI元素
	element := ui.findElementAtPosition(hierarchy.Result.(string), x, y)
	if element == nil {
		return &models.UIFingerprintResponse{
			Success: false,
			Error:   fmt.Sprintf("在坐标(%d,%d)处未找到UI元素", x, y),
		}, nil
	}

	// 添加调试信息
	fmt.Printf("在坐标(%d,%d)处找到元素: text='%s', class='%s', resourceId='%s', bounds=(%d,%d,%d,%d), clickable=%t\n",
		x, y, element.Text, element.Class, element.ResourceID,
		element.X, element.Y, element.Width, element.Height, element.Clickable)

	// 生成Python兼容的指纹
	pythonFingerprint := ui.generatePythonFingerprint(*element)

	// 转换为字符串格式
	fingerprintBytes, err := json.Marshal(pythonFingerprint)
	if err != nil {
		return &models.UIFingerprintResponse{
			Success: false,
			Error:   "序列化指纹失败: " + err.Error(),
		}, nil
	}

	return &models.UIFingerprintResponse{
		Success:     true,
		Fingerprint: string(fingerprintBytes),
	}, nil
}

// MatchUIElement 匹配UI元素
func (ui *IntegratedUIService) MatchUIElement(template map[string]string) (*models.UIMatchResponse, error) {
	// 确保U2服务运行
	if !ui.isU2ServiceRunning() {
		ui.StartU2Service()
		time.Sleep(500 * time.Millisecond)
	}

	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 使用内部匹配方法
	result, err := ui.MatchUIElementInternal(hierarchy.Result.(string), template)
	if err != nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   fmt.Sprintf("匹配失败: %v", err),
		}, nil
	}

	if !result.Found {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "未找到匹配的UI元素",
		}, nil
	}

	return &models.UIMatchResponse{
		Success: true,
		Result:  result,
	}, nil
}

// MatchUIElementByFingerprint 根据指纹匹配UI元素
func (ui *IntegratedUIService) MatchUIElementByFingerprint(fingerprint string) (*models.UIMatchResponse, error) {
	// 确保U2服务运行
	if !ui.isU2ServiceRunning() {
		ui.StartU2Service()
		time.Sleep(500 * time.Millisecond)
	}

	// 尝试解析为紧凑型指纹
	var compactFingerprint CompactFingerprint
	if err := json.Unmarshal([]byte(fingerprint), &compactFingerprint); err == nil {
		return ui.matchByCompactFingerprint(compactFingerprint)
	}

	// 尝试解析为Python指纹
	var pythonFingerprint PythonFingerprint
	if err := json.Unmarshal([]byte(fingerprint), &pythonFingerprint); err == nil {
		return ui.matchByPythonFingerprint(pythonFingerprint)
	}

	// 尝试解析为增强指纹
	var enhancedFingerprint EnhancedFingerprint
	if err := json.Unmarshal([]byte(fingerprint), &enhancedFingerprint); err == nil {
		return ui.matchByEnhancedFingerprint(enhancedFingerprint)
	}

	// 兜底：解析为简单指纹
	var fingerprintData map[string]interface{}
	if err := json.Unmarshal([]byte(fingerprint), &fingerprintData); err != nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "指纹格式错误",
		}, nil
	}

	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 使用简单指纹进行匹配
	result, err := ui.matchByFingerprint(hierarchy.Result.(string), fingerprintData)
	if err != nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   fmt.Sprintf("指纹匹配失败: %v", err),
		}, nil
	}

	if !result.Found {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "未找到匹配的元素",
		}, nil
	}

	return &models.UIMatchResponse{
		Success: true,
		Result:  result,
	}, nil
}

// matchByEnhancedFingerprint 使用增强指纹匹配
func (ui *IntegratedUIService) matchByEnhancedFingerprint(fingerprint EnhancedFingerprint) (*models.UIMatchResponse, error) {
	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 获取当前屏幕尺寸
	currentWidth, currentHeight := ui.getScreenSize()

	// 解析XML并获取所有元素
	var allElements []ElementInfo
	xmlContent := hierarchy.Result.(string)

	var hierarchyData Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchyData); err == nil {
		for _, node := range hierarchyData.Nodes {
			ui.collectAllElements(&node, &allElements)
		}
	} else {
		var root Node
		if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
			return &models.UIMatchResponse{
				Success: false,
				Error:   "XML解析失败",
			}, nil
		}
		ui.collectAllElements(&root, &allElements)
	}

	// 多层级匹配策略
	bestMatch := ui.findBestMatchWithStrategies(allElements, fingerprint, currentWidth, currentHeight)

	if bestMatch == nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "未找到匹配的元素",
		}, nil
	}

	return &models.UIMatchResponse{
		Success: true,
		Result: &MatchResult{
			Found:       true,
			Element:     *bestMatch,
			Confidence:  0.95, // 增强指纹的置信度较高
			MatchReason: "增强指纹匹配",
		},
	}, nil
}

// GetAllUIElements 获取所有UI元素
func (ui *IntegratedUIService) GetAllUIElements() (*models.UIMatchResponse, error) {
	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 解析XML并提取所有元素
	xmlContent := hierarchy.Result.(string)
	var elements []ElementInfo

	// 尝试解析为hierarchy格式
	var hierarchyData Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchyData); err == nil {
		// 成功解析为hierarchy格式
		fmt.Printf("解析为hierarchy格式，包含%d个根节点\n", len(hierarchyData.Nodes))
		for _, node := range hierarchyData.Nodes {
			ui.collectAllElements(&node, &elements)
		}
	} else {
		// 尝试解析为node格式
		var root Node
		if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
			return &models.UIMatchResponse{
				Success: false,
				Error:   fmt.Sprintf("XML解析失败: %v", err),
			}, nil
		}
		fmt.Printf("解析为单个node格式\n")
		ui.collectAllElements(&root, &elements)
	}

	fmt.Printf("总共解析到%d个UI元素\n", len(elements))

	// 统计不同类型的元素
	var clickableCount, textCount, smallCount int
	for _, element := range elements {
		if element.Clickable {
			clickableCount++
		}
		if element.Text != "" {
			textCount++
		}
		if element.Width < 10 || element.Height < 10 {
			smallCount++
		}
	}
	fmt.Printf("其中：可点击元素%d个，有文本元素%d个，小尺寸元素%d个\n", clickableCount, textCount, smallCount)

	return &models.UIMatchResponse{
		Success: true,
		Result:  elements,
	}, nil
}

// Close 关闭服务，清理资源
func (ui *IntegratedUIService) Close() {
	ui.StopU2Service()
	ui.httpClient.CloseIdleConnections()
}

// updateScreenInfo 更新屏幕信息
func (ui *IntegratedUIService) updateScreenInfo() {
	req := JSONRPCRequest{
		JSONRPC: "2.0",
		Method:  "deviceInfo",
		Params:  []interface{}{},
		ID:      1,
	}

	reqData, _ := json.Marshal(req)
	resp, err := ui.httpClient.Post("http://127.0.0.1:9008/jsonrpc/0", "application/json", bytes.NewBuffer(reqData))
	if err != nil {
		return
	}
	defer resp.Body.Close()

	var response JSONRPCResponse
	if err := json.NewDecoder(resp.Body).Decode(&response); err != nil {
		return
	}

	if result, ok := response.Result.(map[string]interface{}); ok {
		if width, ok := result["displayWidth"].(float64); ok {
			ui.screenWidth = int(width)
		}
		if height, ok := result["displayHeight"].(float64); ok {
			ui.screenHeight = int(height)
		}
	}
}

// parseBounds 解析bounds字符串为坐标
func (ui *IntegratedUIService) parseBounds(bounds string) (int, int, int, int) {
	// bounds格式: "[x1,y1][x2,y2]"
	re := regexp.MustCompile(`\[(\d+),(\d+)\]\[(\d+),(\d+)\]`)
	matches := re.FindStringSubmatch(bounds)
	if len(matches) != 5 {
		return 0, 0, 0, 0
	}

	x1, _ := strconv.Atoi(matches[1])
	y1, _ := strconv.Atoi(matches[2])
	x2, _ := strconv.Atoi(matches[3])
	y2, _ := strconv.Atoi(matches[4])

	return x1, y1, x2 - x1, y2 - y1
}

// nodeToElementInfo 将Node转换为ElementInfo
func (ui *IntegratedUIService) nodeToElementInfo(node *Node) ElementInfo {
	x, y, width, height := ui.parseBounds(node.Bounds)

	return ElementInfo{
		Index:         node.Index,
		Text:          node.Text,
		ResourceID:    node.ResourceID,
		Class:         node.Class,
		Package:       node.Package,
		ContentDesc:   node.ContentDesc,
		Checkable:     node.Checkable == "true",
		Checked:       node.Checked == "true",
		Clickable:     node.Clickable == "true",
		Enabled:       node.Enabled == "true",
		Focusable:     node.Focusable == "true",
		Focused:       node.Focused == "true",
		Scrollable:    node.Scrollable == "true",
		LongClickable: node.LongClickable == "true",
		Password:      node.Password == "true",
		Selected:      node.Selected == "true",
		Bounds:        node.Bounds, // 保留原始bounds字符串
		X:             x,
		Y:             y,
		Width:         width,
		Height:        height,
	}
}

// collectAllElements 递归收集所有UI元素
func (ui *IntegratedUIService) collectAllElements(node *Node, elements *[]ElementInfo) {
	element := ui.nodeToElementInfo(node)
	*elements = append(*elements, element)

	for i := range node.Children {
		ui.collectAllElements(&node.Children[i], elements)
	}
}

// MatchUIElementInternal 内部UI元素匹配方法
func (ui *IntegratedUIService) MatchUIElementInternal(xmlContent string, template map[string]string) (*MatchResult, error) {
	// 尝试解析为hierarchy格式
	var hierarchy Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchy); err == nil {
		// 成功解析为hierarchy格式
		for _, node := range hierarchy.Nodes {
			if result := ui.matchNodeRecursive(&node, template); result != nil {
				return result, nil
			}
		}
		return &MatchResult{Found: false}, nil
	}

	// 尝试解析为node格式
	var root Node
	if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
		return nil, fmt.Errorf("XML解析失败: %v", err)
	}

	if result := ui.matchNodeRecursive(&root, template); result != nil {
		return result, nil
	}

	return &MatchResult{Found: false}, nil
}

// matchByFingerprint 根据指纹匹配UI元素
func (ui *IntegratedUIService) matchByFingerprint(xmlContent string, fingerprintData map[string]interface{}) (*MatchResult, error) {
	// 提取指纹中的关键信息
	template := make(map[string]string)

	// 从指纹中提取可用于匹配的属性
	if text, ok := fingerprintData["text"].(string); ok && text != "" {
		template["text"] = text
	}
	if resourceID, ok := fingerprintData["resource_id"].(string); ok && resourceID != "" {
		template["resource-id"] = resourceID
	}
	if class, ok := fingerprintData["class"].(string); ok && class != "" {
		template["class"] = class
	}
	if contentDesc, ok := fingerprintData["content_desc"].(string); ok && contentDesc != "" {
		template["content-desc"] = contentDesc
	}

	// 如果没有可匹配的属性，返回失败
	if len(template) == 0 {
		return &MatchResult{Found: false}, fmt.Errorf("指纹中没有可用于匹配的属性")
	}

	// 使用模板匹配
	return ui.MatchUIElementInternal(xmlContent, template)
}

// matchNodeRecursive 递归匹配节点
func (ui *IntegratedUIService) matchNodeRecursive(node *Node, template map[string]string) *MatchResult {
	confidence := ui.calculateMatchConfidence(node, template)
	if confidence > 0.7 { // 匹配阈值
		element := ui.nodeToElementInfo(node)
		return &MatchResult{
			Found:       true,
			Element:     element,
			Confidence:  confidence,
			MatchReason: "属性匹配",
		}
	}

	// 递归检查子节点
	for i := range node.Children {
		if result := ui.matchNodeRecursive(&node.Children[i], template); result != nil {
			return result
		}
	}

	return nil
}

// calculateMatchConfidence 计算匹配置信度
func (ui *IntegratedUIService) calculateMatchConfidence(node *Node, template map[string]string) float64 {
	totalWeight := 0.0
	matchedWeight := 0.0

	// 定义属性权重
	weights := map[string]float64{
		"text":         1.0,
		"resource-id":  0.9,
		"content-desc": 0.8,
		"class":        0.6,
		"package":      0.4,
		"clickable":    0.3,
		"enabled":      0.2,
	}

	for attr, expectedValue := range template {
		if ui.ignoreAttrs[attr] {
			continue
		}

		weight := weights[attr]
		if weight == 0 {
			weight = 0.1 // 默认权重
		}
		totalWeight += weight

		var actualValue string
		switch attr {
		case "text":
			actualValue = node.Text
		case "resource-id":
			actualValue = node.ResourceID
		case "content-desc":
			actualValue = node.ContentDesc
		case "class":
			actualValue = node.Class
		case "package":
			actualValue = node.Package
		case "clickable":
			actualValue = node.Clickable
		case "enabled":
			actualValue = node.Enabled
		default:
			continue
		}

		if ui.isValueMatch(actualValue, expectedValue) {
			matchedWeight += weight
		}
	}

	if totalWeight == 0 {
		return 0
	}

	return matchedWeight / totalWeight
}

// isValueMatch 检查值是否匹配
func (ui *IntegratedUIService) isValueMatch(actual, expected string) bool {
	if actual == expected {
		return true
	}

	// 支持模糊匹配
	if strings.Contains(strings.ToLower(actual), strings.ToLower(expected)) {
		return true
	}

	return false
}

// PythonFingerprint Python脚本兼容的指纹结构
type PythonFingerprint struct {
	BasicAttributes struct {
		Text        string `json:"text"`
		Class       string `json:"class"`
		ResourceID  string `json:"resource_id"`
		ContentDesc string `json:"content_desc"`
		Package     string `json:"package"`
		Index       string `json:"index"`
	} `json:"basic_attributes"`
	PositionFeatures struct {
		Bounds         string  `json:"bounds"`
		CenterX        int     `json:"center_x"`
		CenterY        int     `json:"center_y"`
		Width          int     `json:"width"`
		Height         int     `json:"height"`
		RelativeX      float64 `json:"relative_x"`
		RelativeY      float64 `json:"relative_y"`
		RelativeWidth  float64 `json:"relative_width"`
		RelativeHeight float64 `json:"relative_height"`
	} `json:"position_features"`
	StateFeatures struct {
		Clickable  bool `json:"clickable"`
		Enabled    bool `json:"enabled"`
		Focusable  bool `json:"focusable"`
		Checkable  bool `json:"checkable"`
		Scrollable bool `json:"scrollable"`
		Selected   bool `json:"selected"`
		Password   bool `json:"password"`
	} `json:"state_features"`
	ContextFeatures struct {
		ParentClass      string `json:"parent_class"`
		ParentResourceID string `json:"parent_resource_id"`
		SiblingCount     int    `json:"sibling_count"`
		ElementIndex     int    `json:"element_index"`
	} `json:"context_features"`
	FingerprintHash string `json:"fingerprint_hash"`
	UniquenessScore int    `json:"uniqueness_score"`
}

// CompactFingerprint 紧凑型指纹结构
type CompactFingerprint struct {
	Hash        string             `json:"hash"`
	Score       int                `json:"score"`
	ID          string             `json:"id,omitempty"`
	Text        string             `json:"text,omitempty"`
	Desc        string             `json:"desc,omitempty"`
	Class       string             `json:"class,omitempty"`
	Pos         map[string]float64 `json:"pos,omitempty"`
	ParentID    string             `json:"parent_id,omitempty"`
	ParentClass string             `json:"parent_class,omitempty"`
	States      map[string]bool    `json:"states,omitempty"`
}

// generatePythonFingerprint 生成Python兼容的指纹
func (ui *IntegratedUIService) generatePythonFingerprint(element ElementInfo) PythonFingerprint {
	// 获取屏幕尺寸
	screenWidth, screenHeight := ui.getScreenSize()

	// 计算位置信息
	centerX := element.X + element.Width/2
	centerY := element.Y + element.Height/2

	// 生成bounds字符串
	bounds := fmt.Sprintf("[%d,%d][%d,%d]", element.X, element.Y, element.X+element.Width, element.Y+element.Height)

	fingerprint := PythonFingerprint{
		BasicAttributes: struct {
			Text        string `json:"text"`
			Class       string `json:"class"`
			ResourceID  string `json:"resource_id"`
			ContentDesc string `json:"content_desc"`
			Package     string `json:"package"`
			Index       string `json:"index"`
		}{
			Text:        element.Text,
			Class:       element.Class,
			ResourceID:  element.ResourceID,
			ContentDesc: element.ContentDesc,
			Package:     element.Package,
			Index:       element.Index, // Index已经是string类型
		},
		PositionFeatures: struct {
			Bounds         string  `json:"bounds"`
			CenterX        int     `json:"center_x"`
			CenterY        int     `json:"center_y"`
			Width          int     `json:"width"`
			Height         int     `json:"height"`
			RelativeX      float64 `json:"relative_x"`
			RelativeY      float64 `json:"relative_y"`
			RelativeWidth  float64 `json:"relative_width"`
			RelativeHeight float64 `json:"relative_height"`
		}{
			Bounds:         bounds,
			CenterX:        centerX,
			CenterY:        centerY,
			Width:          element.Width,
			Height:         element.Height,
			RelativeX:      float64(centerX) / float64(screenWidth),
			RelativeY:      float64(centerY) / float64(screenHeight),
			RelativeWidth:  float64(element.Width) / float64(screenWidth),
			RelativeHeight: float64(element.Height) / float64(screenHeight),
		},
		StateFeatures: struct {
			Clickable  bool `json:"clickable"`
			Enabled    bool `json:"enabled"`
			Focusable  bool `json:"focusable"`
			Checkable  bool `json:"checkable"`
			Scrollable bool `json:"scrollable"`
			Selected   bool `json:"selected"`
			Password   bool `json:"password"`
		}{
			Clickable:  element.Clickable,
			Enabled:    element.Enabled,
			Focusable:  element.Focusable,
			Checkable:  element.Checkable,
			Scrollable: element.Scrollable,
			Selected:   element.Selected,
			Password:   element.Password,
		},
		ContextFeatures: ui.extractContextFeatures(element),
		FingerprintHash: ui.generateFingerprintHash(element),
		UniquenessScore: ui.calculateUniquenessScore(element),
	}

	return fingerprint
}

// extractContextFeatures 提取上下文特征
func (ui *IntegratedUIService) extractContextFeatures(element ElementInfo) struct {
	ParentClass      string `json:"parent_class"`
	ParentResourceID string `json:"parent_resource_id"`
	SiblingCount     int    `json:"sibling_count"`
	ElementIndex     int    `json:"element_index"`
} {
	// 这里需要从UI层级中获取父元素信息，暂时返回空值
	return struct {
		ParentClass      string `json:"parent_class"`
		ParentResourceID string `json:"parent_resource_id"`
		SiblingCount     int    `json:"sibling_count"`
		ElementIndex     int    `json:"element_index"`
	}{
		ParentClass:      "",
		ParentResourceID: "",
		SiblingCount:     0,
		ElementIndex:     -1,
	}
}

// generateFingerprintHash 生成指纹哈希
func (ui *IntegratedUIService) generateFingerprintHash(element ElementInfo) string {
	// 组合关键特征
	key := fmt.Sprintf("%s|%s|%s|%s|%d|%d|%d|%d|%t",
		element.Text, element.Class, element.ResourceID, element.ContentDesc,
		element.X, element.Y, element.Width, element.Height, element.Clickable)

	// 使用md5计算哈希
	hash := md5.Sum([]byte(key))
	return fmt.Sprintf("%x", hash)[:16] // 取前16位
}

// calculateUniquenessScore 计算唯一性评分
func (ui *IntegratedUIService) calculateUniquenessScore(element ElementInfo) int {
	score := 0

	// 文本内容权重
	if element.Text != "" {
		score += 30
	}

	// 资源ID权重
	if element.ResourceID != "" {
		score += 25
	}

	// 内容描述权重
	if element.ContentDesc != "" {
		score += 20
	}

	// 类名权重
	if element.Class != "" {
		score += 10
	}

	// 可点击性权重
	if element.Clickable {
		score += 10
	}

	// 位置信息权重
	if element.X >= 0 && element.Y >= 0 && element.Width > 0 && element.Height > 0 {
		score += 5
	}

	if score > 100 {
		score = 100
	}

	return score
}

// matchByCompactFingerprint 使用紧凑型指纹匹配
func (ui *IntegratedUIService) matchByCompactFingerprint(fingerprint CompactFingerprint) (*models.UIMatchResponse, error) {
	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 解析XML并获取所有元素
	var allElements []ElementInfo
	xmlContent := hierarchy.Result.(string)

	var hierarchyData Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchyData); err == nil {
		for _, node := range hierarchyData.Nodes {
			ui.collectAllElements(&node, &allElements)
		}
	} else {
		var root Node
		if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
			return &models.UIMatchResponse{
				Success: false,
				Error:   "XML解析失败",
			}, nil
		}
		ui.collectAllElements(&root, &allElements)
	}

	// 使用紧凑型指纹匹配策略
	bestMatch := ui.findBestMatchByCompactFingerprint(allElements, fingerprint)

	if bestMatch == nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "未找到匹配的元素",
		}, nil
	}

	// 构建匹配结果
	result := &MatchResult{
		Found:       true,
		Element:     *bestMatch,
		Confidence:  0.95, // 紧凑型指纹匹配置信度
		MatchReason: "紧凑型指纹匹配成功",
	}

	return &models.UIMatchResponse{
		Success: true,
		Result:  result,
	}, nil
}

// findBestMatchByCompactFingerprint 智能多层级指纹匹配算法
func (ui *IntegratedUIService) findBestMatchByCompactFingerprint(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	fmt.Printf("🔍 开始指纹匹配，指纹信息: ID=%s, Text=%s, Class=%s\n",
		fingerprint.ID, fingerprint.Text, fingerprint.Class)

	// 策略1: 资源ID精确匹配 - 最高优先级
	if fingerprint.ID != "" {
		if idMatch := ui.findByResourceIDAndPosition(elements, fingerprint); idMatch != nil {
			fmt.Printf("✅ 资源ID匹配成功: %s\n", fingerprint.ID)
			return idMatch
		}
		fmt.Printf("❌ 资源ID匹配失败: %s\n", fingerprint.ID)
	}

	// 策略2: 文本精确匹配
	if fingerprint.Text != "" {
		if textMatch := ui.findByExactText(elements, fingerprint); textMatch != nil {
			fmt.Printf("✅ 文本匹配成功: %s\n", fingerprint.Text)
			return textMatch
		}
		fmt.Printf("❌ 文本匹配失败: %s\n", fingerprint.Text)
	}

	// 策略3: 多特征组合匹配
	if comboMatch := ui.findByFeatureCombination(elements, fingerprint); comboMatch != nil {
		fmt.Printf("✅ 组合特征匹配成功\n")
		return comboMatch
	}

	// 策略4: 位置兜底匹配
	if posMatch := ui.findByPositionFallback(elements, fingerprint); posMatch != nil {
		fmt.Printf("✅ 位置匹配成功\n")
		return posMatch
	}

	fmt.Printf("❌ 所有匹配策略都失败了\n")
	return nil
}

// findByHashMatch 哈希精确匹配 - 最高优先级
func (ui *IntegratedUIService) findByHashMatch(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	if fingerprint.Hash == "" {
		return nil
	}

	for _, element := range elements {
		// 生成元素的哈希（按照Python脚本的逻辑）
		elementHash := ui.generateElementHash(element)
		if elementHash == fingerprint.Hash {
			return &element
		}
	}
	return nil
}

// generateElementHash 按照Python脚本逻辑生成元素哈希
func (ui *IntegratedUIService) generateElementHash(element ElementInfo) string {
	screenWidth, screenHeight := ui.getScreenSize()

	// 计算相对位置
	centerX := element.X + element.Width/2
	centerY := element.Y + element.Height/2
	relX := float64(centerX) / float64(screenWidth)
	relY := float64(centerY) / float64(screenHeight)

	// 按照Python脚本的哈希组件顺序
	hashComponents := []string{
		element.ResourceID,
		element.Text,
		element.ContentDesc,
		element.Class,
		fmt.Sprintf("%.3f,%.3f", relX, relY),
		element.Index, // Index已经是string类型
	}

	// 过滤空值并组合
	var validComponents []string
	for _, comp := range hashComponents {
		if comp != "" && comp != "0.000,0.000" {
			validComponents = append(validComponents, comp)
		}
	}

	combined := strings.Join(validComponents, "|")
	hash := md5.Sum([]byte(combined))
	return fmt.Sprintf("%x", hash)[:8] // 取前8位
}

// findByResourceIDAndPosition 资源ID + 位置验证
func (ui *IntegratedUIService) findByResourceIDAndPosition(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	if fingerprint.ID == "" {
		return nil
	}

	var candidates []ElementInfo
	// 收集所有资源ID匹配的元素
	for _, element := range elements {
		if element.ResourceID == fingerprint.ID {
			candidates = append(candidates, element)
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	// 如果只有一个候选，直接返回
	if len(candidates) == 1 {
		return &candidates[0]
	}

	// 多个候选时，使用位置信息筛选
	if fingerprint.Pos != nil {
		return ui.findClosestByPosition(candidates, fingerprint.Pos)
	}

	// 如果没有位置信息，返回第一个
	return &candidates[0]
}

// findByExactText 精确文本匹配
func (ui *IntegratedUIService) findByExactText(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	if fingerprint.Text == "" {
		return nil
	}

	var candidates []ElementInfo
	// 收集所有文本匹配的元素
	for _, element := range elements {
		if element.Text == fingerprint.Text {
			candidates = append(candidates, element)
		}
	}

	if len(candidates) == 0 {
		return nil
	}

	// 如果只有一个候选，直接返回
	if len(candidates) == 1 {
		return &candidates[0]
	}

	// 多个候选时，使用位置信息筛选
	if fingerprint.Pos != nil {
		return ui.findClosestByPosition(candidates, fingerprint.Pos)
	}

	// 如果没有位置信息，返回第一个
	return &candidates[0]
}

// findClosestByPosition 根据位置信息找到最接近的元素
func (ui *IntegratedUIService) findClosestByPosition(candidates []ElementInfo, pos map[string]float64) *ElementInfo {
	if pos == nil {
		return nil
	}

	x, hasX := pos["x"]
	y, hasY := pos["y"]
	if !hasX || !hasY {
		return nil
	}

	var closestMatch *ElementInfo
	var minDistance float64 = math.MaxFloat64

	screenWidth, screenHeight := ui.getScreenSize()

	for _, element := range candidates {
		elementCenterX := element.X + element.Width/2
		elementCenterY := element.Y + element.Height/2
		elementRelX := float64(elementCenterX) / float64(screenWidth)
		elementRelY := float64(elementCenterY) / float64(screenHeight)

		dx := math.Abs(elementRelX - x)
		dy := math.Abs(elementRelY - y)
		distance := math.Sqrt(dx*dx + dy*dy)

		if distance < minDistance {
			minDistance = distance
			closestMatch = &element
		}
	}

	// 只有在距离合理的情况下才返回匹配
	if closestMatch != nil && minDistance <= 0.15 { // 15%的位置误差，更宽松
		fmt.Printf("🎯 位置匹配成功，距离: %.3f\n", minDistance)
		return closestMatch
	}

	fmt.Printf("❌ 位置匹配失败，最小距离: %.3f (阈值: 0.15)\n", minDistance)
	return nil
}

// findByFeatureCombination 多特征组合匹配
func (ui *IntegratedUIService) findByFeatureCombination(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	var bestMatch *ElementInfo
	var bestScore float64 = 0

	for _, element := range elements {
		score := ui.calculateSmartMatchScore(element, fingerprint)
		if score > bestScore && score >= 0.85 { // 高阈值确保精确匹配
			bestScore = score
			bestMatch = &element
		}
	}

	return bestMatch
}

// findByPositionFallback 位置兜底匹配
func (ui *IntegratedUIService) findByPositionFallback(elements []ElementInfo, fingerprint CompactFingerprint) *ElementInfo {
	if fingerprint.Pos == nil {
		return nil
	}

	// 按类名筛选候选元素
	var candidates []ElementInfo
	if fingerprint.Class != "" {
		for _, element := range elements {
			if element.Class == fingerprint.Class {
				candidates = append(candidates, element)
			}
		}
	} else {
		candidates = elements
	}

	return ui.findClosestByPosition(candidates, fingerprint.Pos)
}

// calculateSmartMatchScore 智能匹配分数计算
func (ui *IntegratedUIService) calculateSmartMatchScore(element ElementInfo, fingerprint CompactFingerprint) float64 {
	score := 0.0
	totalWeight := 0.0

	// 资源ID匹配 (权重: 0.4) - 最重要
	if fingerprint.ID != "" {
		totalWeight += 0.4
		if element.ResourceID == fingerprint.ID {
			score += 0.4
		}
	}

	// 文本匹配 (权重: 0.3)
	if fingerprint.Text != "" {
		totalWeight += 0.3
		if element.Text == fingerprint.Text {
			score += 0.3
		} else if strings.Contains(element.Text, fingerprint.Text) ||
			strings.Contains(fingerprint.Text, element.Text) {
			score += 0.15 // 部分匹配
		}
	}

	// 内容描述匹配 (权重: 0.15)
	if fingerprint.Desc != "" {
		totalWeight += 0.15
		if element.ContentDesc == fingerprint.Desc {
			score += 0.15
		}
	}

	// 类名匹配 (权重: 0.1)
	if fingerprint.Class != "" {
		totalWeight += 0.1
		if element.Class == fingerprint.Class {
			score += 0.1
		}
	}

	// 位置验证 (权重: 0.05) - 辅助验证
	if fingerprint.Pos != nil {
		if x, ok := fingerprint.Pos["x"]; ok {
			if y, ok2 := fingerprint.Pos["y"]; ok2 {
				totalWeight += 0.05

				screenWidth, screenHeight := ui.getScreenSize()
				elementCenterX := element.X + element.Width/2
				elementCenterY := element.Y + element.Height/2
				elementRelX := float64(elementCenterX) / float64(screenWidth)
				elementRelY := float64(elementCenterY) / float64(screenHeight)

				dx := math.Abs(elementRelX - x)
				dy := math.Abs(elementRelY - y)
				distance := math.Sqrt(dx*dx + dy*dy)

				if distance <= 0.05 { // 5%的位置误差
					positionScore := (0.05 - distance) / 0.05 * 0.05
					score += positionScore
				}
			}
		}
	}

	if totalWeight == 0 {
		return 0
	}

	return score / totalWeight
}

// calculateCompactFingerprintScore 计算紧凑型指纹匹配分数
func (ui *IntegratedUIService) calculateCompactFingerprintScore(element ElementInfo, fingerprint CompactFingerprint) float64 {
	score := 0.0
	totalWeight := 0.0

	// 资源ID匹配 (权重: 0.5) - 最高权重
	if fingerprint.ID != "" {
		totalWeight += 0.5
		if element.ResourceID == fingerprint.ID {
			score += 0.5
		}
	}

	// 文本匹配 (权重: 0.3)
	if fingerprint.Text != "" {
		totalWeight += 0.3
		if element.Text == fingerprint.Text {
			score += 0.3
		} else if strings.Contains(element.Text, fingerprint.Text) ||
			strings.Contains(fingerprint.Text, element.Text) {
			score += 0.15 // 部分匹配
		}
	}

	// 类名匹配 (权重: 0.1)
	if fingerprint.Class != "" {
		totalWeight += 0.1
		if element.Class == fingerprint.Class {
			score += 0.1
		}
	}

	// 内容描述匹配 (权重: 0.1)
	if fingerprint.Desc != "" {
		totalWeight += 0.1
		if element.ContentDesc == fingerprint.Desc {
			score += 0.1
		}
	}

	// 如果没有精确的ID或文本匹配，使用位置验证
	hasExactMatch := (fingerprint.ID != "" && element.ResourceID == fingerprint.ID) ||
		(fingerprint.Text != "" && element.Text == fingerprint.Text)

	if !hasExactMatch && fingerprint.Pos != nil {
		if x, ok := fingerprint.Pos["x"]; ok {
			if y, ok2 := fingerprint.Pos["y"]; ok2 {
				totalWeight += 0.2

				// 获取屏幕尺寸
				screenWidth, screenHeight := ui.getScreenSize()

				// 计算元素的相对位置
				elementCenterX := element.X + element.Width/2
				elementCenterY := element.Y + element.Height/2
				elementRelX := float64(elementCenterX) / float64(screenWidth)
				elementRelY := float64(elementCenterY) / float64(screenHeight)

				// 计算位置差异
				dx := math.Abs(elementRelX - x)
				dy := math.Abs(elementRelY - y)
				distance := math.Sqrt(dx*dx + dy*dy)

				// 位置匹配分数（距离越近分数越高）
				if distance <= 0.1 { // 允许10%的位置误差
					positionScore := (0.1 - distance) / 0.1 * 0.2
					score += positionScore
				}
			}
		}
	}

	// 如果没有任何可匹配的特征，返回0
	if totalWeight == 0 {
		return 0
	}

	// 归一化分数
	normalizedScore := score / totalWeight

	// 如果有精确匹配，给予额外加分
	if hasExactMatch {
		normalizedScore = math.Min(normalizedScore*1.2, 1.0)
	}

	return normalizedScore
}

// matchByPythonFingerprint 使用Python指纹匹配
func (ui *IntegratedUIService) matchByPythonFingerprint(fingerprint PythonFingerprint) (*models.UIMatchResponse, error) {
	// 获取UI层级
	hierarchy, err := ui.GetUIHierarchy()
	if err != nil || !hierarchy.Success {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "获取UI层级失败",
		}, nil
	}

	// 解析XML并获取所有元素
	var allElements []ElementInfo
	xmlContent := hierarchy.Result.(string)

	var hierarchyData Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchyData); err == nil {
		for _, node := range hierarchyData.Nodes {
			ui.collectAllElements(&node, &allElements)
		}
	} else {
		var root Node
		if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
			return &models.UIMatchResponse{
				Success: false,
				Error:   "XML解析失败",
			}, nil
		}
		ui.collectAllElements(&root, &allElements)
	}

	// 使用Python指纹匹配策略
	bestMatch := ui.findBestMatchByPythonFingerprint(allElements, fingerprint)

	if bestMatch == nil {
		return &models.UIMatchResponse{
			Success: false,
			Error:   "未找到匹配的元素",
		}, nil
	}

	// 构建匹配结果
	result := &MatchResult{
		Found:       true,
		Element:     *bestMatch,
		Confidence:  0.9, // Python指纹匹配置信度
		MatchReason: "Python指纹匹配成功",
	}

	return &models.UIMatchResponse{
		Success: true,
		Result:  result,
	}, nil
}

// findBestMatchByPythonFingerprint 使用Python指纹查找最佳匹配
func (ui *IntegratedUIService) findBestMatchByPythonFingerprint(elements []ElementInfo, fingerprint PythonFingerprint) *ElementInfo {
	var bestMatch *ElementInfo
	var bestScore float64 = 0

	// 优先尝试精确匹配
	for _, element := range elements {
		score := ui.calculatePythonFingerprintScore(element, fingerprint)

		// 如果有资源ID或文本完全匹配，优先选择
		if (fingerprint.BasicAttributes.ResourceID != "" && element.ResourceID == fingerprint.BasicAttributes.ResourceID) ||
			(fingerprint.BasicAttributes.Text != "" && element.Text == fingerprint.BasicAttributes.Text) {
			if score > bestScore && score >= 0.8 { // 精确匹配需要80%以上
				bestScore = score
				bestMatch = &element
			}
		} else if score > bestScore && score >= 0.7 { // 普通匹配需要70%以上
			bestScore = score
			bestMatch = &element
		}
	}

	// 如果没有找到高分匹配，尝试位置匹配
	if bestMatch == nil && fingerprint.PositionFeatures.Bounds != "" {
		return ui.findElementByPosition(elements, fingerprint.PositionFeatures)
	}

	return bestMatch
}

// calculatePythonFingerprintScore 计算Python指纹匹配分数
func (ui *IntegratedUIService) calculatePythonFingerprintScore(element ElementInfo, fingerprint PythonFingerprint) float64 {
	score := 0.0
	totalWeight := 0.0

	// 文本匹配 (权重: 0.4) - 提高文本匹配权重
	if fingerprint.BasicAttributes.Text != "" {
		totalWeight += 0.4
		if element.Text == fingerprint.BasicAttributes.Text {
			score += 0.4
		} else if strings.Contains(element.Text, fingerprint.BasicAttributes.Text) ||
			strings.Contains(fingerprint.BasicAttributes.Text, element.Text) {
			score += 0.2 // 部分匹配
		}
	}

	// 资源ID匹配 (权重: 0.35) - 提高资源ID权重
	if fingerprint.BasicAttributes.ResourceID != "" {
		totalWeight += 0.35
		if element.ResourceID == fingerprint.BasicAttributes.ResourceID {
			score += 0.35
		}
	}

	// 类名匹配 (权重: 0.15)
	if fingerprint.BasicAttributes.Class != "" {
		totalWeight += 0.15
		if element.Class == fingerprint.BasicAttributes.Class {
			score += 0.15
		}
	}

	// 内容描述匹配 (权重: 0.1)
	if fingerprint.BasicAttributes.ContentDesc != "" {
		totalWeight += 0.1
		if element.ContentDesc == fingerprint.BasicAttributes.ContentDesc {
			score += 0.1
		}
	}

	// 如果有精确的文本或资源ID匹配，不需要其他验证
	hasExactMatch := (fingerprint.BasicAttributes.Text != "" && element.Text == fingerprint.BasicAttributes.Text) ||
		(fingerprint.BasicAttributes.ResourceID != "" && element.ResourceID == fingerprint.BasicAttributes.ResourceID)

	if !hasExactMatch {
		// 位置验证 (权重: 0.2) - 只在没有精确匹配时使用
		if fingerprint.PositionFeatures.CenterX > 0 && fingerprint.PositionFeatures.CenterY > 0 {
			totalWeight += 0.2
			elementCenterX := element.X + element.Width/2
			elementCenterY := element.Y + element.Height/2

			// 计算位置相似度
			dx := float64(elementCenterX - fingerprint.PositionFeatures.CenterX)
			dy := float64(elementCenterY - fingerprint.PositionFeatures.CenterY)
			distance := math.Sqrt(dx*dx + dy*dy)

			// 距离越近分数越高，最大允许100像素误差
			if distance <= 100 {
				positionScore := (100 - distance) / 100 * 0.2
				score += positionScore
			}
		}
	}

	// 如果没有任何可匹配的特征，返回0
	if totalWeight == 0 {
		return 0
	}

	// 归一化分数
	normalizedScore := score / totalWeight

	// 如果有精确匹配，给予额外加分
	if hasExactMatch {
		normalizedScore = math.Min(normalizedScore*1.1, 1.0)
	}

	return normalizedScore
}

// findElementByPosition 根据位置信息查找元素
func (ui *IntegratedUIService) findElementByPosition(elements []ElementInfo, positionFeatures struct {
	Bounds         string  `json:"bounds"`
	CenterX        int     `json:"center_x"`
	CenterY        int     `json:"center_y"`
	Width          int     `json:"width"`
	Height         int     `json:"height"`
	RelativeX      float64 `json:"relative_x"`
	RelativeY      float64 `json:"relative_y"`
	RelativeWidth  float64 `json:"relative_width"`
	RelativeHeight float64 `json:"relative_height"`
}) *ElementInfo {
	// 解析目标bounds
	targetBounds := positionFeatures.Bounds
	if targetBounds == "" {
		return nil
	}

	// 使用正则表达式解析bounds
	re := regexp.MustCompile(`\[(\d+),(\d+)\]\[(\d+),(\d+)\]`)
	matches := re.FindStringSubmatch(targetBounds)
	if len(matches) != 5 {
		return nil
	}

	targetX1, _ := strconv.Atoi(matches[1])
	targetY1, _ := strconv.Atoi(matches[2])
	targetX2, _ := strconv.Atoi(matches[3])
	targetY2, _ := strconv.Atoi(matches[4])

	targetCenterX := (targetX1 + targetX2) / 2
	targetCenterY := (targetY1 + targetY2) / 2
	targetWidth := targetX2 - targetX1
	targetHeight := targetY2 - targetY1

	var bestMatch *ElementInfo
	var minDistance float64 = math.MaxFloat64

	// 查找位置最接近的元素
	for _, element := range elements {
		elementCenterX := element.X + element.Width/2
		elementCenterY := element.Y + element.Height/2

		// 计算中心点距离
		dx := float64(elementCenterX - targetCenterX)
		dy := float64(elementCenterY - targetCenterY)
		distance := math.Sqrt(dx*dx + dy*dy)

		// 检查尺寸是否相似（允许20%的误差）
		widthRatio := float64(element.Width) / float64(targetWidth)
		heightRatio := float64(element.Height) / float64(targetHeight)

		if widthRatio >= 0.8 && widthRatio <= 1.2 && heightRatio >= 0.8 && heightRatio <= 1.2 {
			if distance < minDistance && distance < 50 { // 距离阈值50像素
				minDistance = distance
				bestMatch = &element
			}
		}
	}

	return bestMatch
}

// EnhancedFingerprint 增强的指纹结构
type EnhancedFingerprint struct {
	Primary struct {
		Class         string             `json:"class"`
		Clickable     bool               `json:"clickable"`
		PositionRatio map[string]float64 `json:"position_ratio"` // x, y 屏幕百分比
		SizeRange     map[string]int     `json:"size_range"`     // min_width, max_width, min_height, max_height
		ScreenRegion  string             `json:"screen_region"`  // top_left, top_right, center, etc.
	} `json:"primary"`
	Secondary struct {
		TextPattern        string `json:"text_pattern,omitempty"`
		ResourceIDPattern  string `json:"resource_id_pattern,omitempty"`
		ContentDescPattern string `json:"content_desc_pattern,omitempty"`
	} `json:"secondary"`
	Context struct {
		ParentClass  string `json:"parent_class,omitempty"`
		SiblingCount int    `json:"sibling_count,omitempty"`
		ElementIndex int    `json:"element_index,omitempty"`
		LayerDepth   int    `json:"layer_depth,omitempty"`
	} `json:"context"`
	Fallback struct {
		XPath       string         `json:"xpath,omitempty"`
		AbsolutePos map[string]int `json:"absolute_pos,omitempty"` // 绝对位置兜底
	} `json:"fallback"`
	Metadata struct {
		CreatedAt    string  `json:"created_at"`
		ScreenWidth  int     `json:"screen_width"`
		ScreenHeight int     `json:"screen_height"`
		Confidence   float64 `json:"confidence"`
		Description  string  `json:"description,omitempty"`
	} `json:"metadata"`
}

// GenerateEnhancedFingerprint 生成增强指纹
func (ui *IntegratedUIService) GenerateEnhancedFingerprint(element ElementInfo, description string) (*models.UIMatchResponse, error) {
	// 获取屏幕尺寸
	screenWidth, screenHeight := ui.getScreenSize()

	fingerprint := EnhancedFingerprint{}

	// 主要特征
	fingerprint.Primary.Class = element.Class
	fingerprint.Primary.Clickable = element.Clickable

	// 计算位置百分比
	centerX := element.X + element.Width/2
	centerY := element.Y + element.Height/2
	fingerprint.Primary.PositionRatio = map[string]float64{
		"x": float64(centerX) / float64(screenWidth),
		"y": float64(centerY) / float64(screenHeight),
	}

	// 尺寸范围（允许±20%变化）
	fingerprint.Primary.SizeRange = map[string]int{
		"min_width":  int(float64(element.Width) * 0.8),
		"max_width":  int(float64(element.Width) * 1.2),
		"min_height": int(float64(element.Height) * 0.8),
		"max_height": int(float64(element.Height) * 1.2),
	}

	// 屏幕区域
	fingerprint.Primary.ScreenRegion = ui.getScreenRegion(centerX, centerY, screenWidth, screenHeight)

	// 次要特征
	if element.Text != "" {
		fingerprint.Secondary.TextPattern = ui.generateTextPattern(element.Text)
	}
	if element.ResourceID != "" {
		fingerprint.Secondary.ResourceIDPattern = ui.generateResourceIDPattern(element.ResourceID)
	}
	if element.ContentDesc != "" {
		fingerprint.Secondary.ContentDescPattern = ui.generateContentDescPattern(element.ContentDesc)
	}

	// 上下文信息（需要从UI层级中获取）
	context, err := ui.getElementContext(element)
	if err == nil {
		fingerprint.Context = context
	}

	// 兜底策略
	fingerprint.Fallback.XPath = ui.generateXPath(element)
	fingerprint.Fallback.AbsolutePos = map[string]int{
		"x": centerX,
		"y": centerY,
	}

	// 元数据
	fingerprint.Metadata.CreatedAt = time.Now().Format("2006-01-02 15:04:05")
	fingerprint.Metadata.ScreenWidth = screenWidth
	fingerprint.Metadata.ScreenHeight = screenHeight
	fingerprint.Metadata.Confidence = ui.calculateFingerprintConfidence(fingerprint)
	fingerprint.Metadata.Description = description

	return &models.UIMatchResponse{
		Success: true,
		Result:  fingerprint,
	}, nil
}

// getScreenSize 获取屏幕尺寸
func (ui *IntegratedUIService) getScreenSize() (int, int) {
	if ui.screenWidth > 0 && ui.screenHeight > 0 {
		return ui.screenWidth, ui.screenHeight
	}

	// 通过adb获取屏幕尺寸
	cmd := exec.Command("adb", "shell", "wm", "size")
	output, err := cmd.Output()
	if err != nil {
		return 1080, 1920 // 默认值
	}

	// 解析输出: Physical size: 1080x1920
	re := regexp.MustCompile(`(\d+)x(\d+)`)
	matches := re.FindStringSubmatch(string(output))
	if len(matches) == 3 {
		width, _ := strconv.Atoi(matches[1])
		height, _ := strconv.Atoi(matches[2])
		ui.screenWidth = width
		ui.screenHeight = height
		return width, height
	}

	return 1080, 1920
}

// getScreenRegion 获取屏幕区域
func (ui *IntegratedUIService) getScreenRegion(x, y, screenWidth, screenHeight int) string {
	xRatio := float64(x) / float64(screenWidth)
	yRatio := float64(y) / float64(screenHeight)

	var region string

	// 垂直区域
	if yRatio < 0.33 {
		region = "top"
	} else if yRatio > 0.67 {
		region = "bottom"
	} else {
		region = "center"
	}

	// 水平区域
	if xRatio < 0.33 {
		region += "_left"
	} else if xRatio > 0.67 {
		region += "_right"
	} else {
		region += "_center"
	}

	return region
}

// generateTextPattern 生成文本模式
func (ui *IntegratedUIService) generateTextPattern(text string) string {
	// 如果是常见的关闭/跳过文本，生成通用模式
	closeKeywords := []string{"关闭", "跳过", "×", "✕", "close", "skip", "dismiss"}
	for _, keyword := range closeKeywords {
		if strings.Contains(strings.ToLower(text), strings.ToLower(keyword)) {
			return "关闭|跳过|×|✕|close|skip|dismiss"
		}
	}

	// 如果包含数字，替换为通配符
	re := regexp.MustCompile(`\d+`)
	pattern := re.ReplaceAllString(text, `\d+`)

	// 转义特殊字符
	pattern = regexp.QuoteMeta(pattern)
	pattern = strings.ReplaceAll(pattern, `\\d\+`, `\d+`)

	return pattern
}

// generateResourceIDPattern 生成资源ID模式
func (ui *IntegratedUIService) generateResourceIDPattern(resourceID string) string {
	// 提取关键部分
	parts := strings.Split(resourceID, "/")
	if len(parts) > 1 {
		idPart := parts[len(parts)-1]
		// 生成模糊匹配模式
		return ".*" + regexp.QuoteMeta(idPart) + ".*"
	}
	return regexp.QuoteMeta(resourceID)
}

// generateContentDescPattern 生成内容描述模式
func (ui *IntegratedUIService) generateContentDescPattern(contentDesc string) string {
	// 类似文本模式的处理
	return ui.generateTextPattern(contentDesc)
}

// getElementContext 获取元素上下文信息
func (ui *IntegratedUIService) getElementContext(element ElementInfo) (struct {
	ParentClass  string `json:"parent_class,omitempty"`
	SiblingCount int    `json:"sibling_count,omitempty"`
	ElementIndex int    `json:"element_index,omitempty"`
	LayerDepth   int    `json:"layer_depth,omitempty"`
}, error) {
	context := struct {
		ParentClass  string `json:"parent_class,omitempty"`
		SiblingCount int    `json:"sibling_count,omitempty"`
		ElementIndex int    `json:"element_index,omitempty"`
		LayerDepth   int    `json:"layer_depth,omitempty"`
	}{}

	// 这里需要从完整的UI层级中分析，暂时返回基本信息
	// TODO: 实现完整的上下文分析
	if element.Index != "" {
		if idx, err := strconv.Atoi(element.Index); err == nil {
			context.ElementIndex = idx
		}
	}

	return context, nil
}

// generateXPath 生成XPath
func (ui *IntegratedUIService) generateXPath(element ElementInfo) string {
	xpath := "//"

	// 添加类名
	if element.Class != "" {
		xpath += element.Class
	} else {
		xpath += "*"
	}

	// 添加属性条件
	var conditions []string

	if element.Clickable {
		conditions = append(conditions, "@clickable='true'")
	}

	if element.Text != "" {
		conditions = append(conditions, fmt.Sprintf("@text='%s'", element.Text))
	}

	if element.ResourceID != "" {
		conditions = append(conditions, fmt.Sprintf("@resource-id='%s'", element.ResourceID))
	}

	if element.Index != "" {
		conditions = append(conditions, fmt.Sprintf("@index='%s'", element.Index))
	}

	if len(conditions) > 0 {
		xpath += "[" + strings.Join(conditions, " and ") + "]"
	}

	return xpath
}

// calculateFingerprintConfidence 计算指纹置信度
func (ui *IntegratedUIService) calculateFingerprintConfidence(fingerprint EnhancedFingerprint) float64 {
	confidence := 0.0

	// 主要特征权重
	if fingerprint.Primary.Class != "" {
		confidence += 0.3
	}
	if fingerprint.Primary.Clickable {
		confidence += 0.2
	}
	if fingerprint.Primary.ScreenRegion != "" {
		confidence += 0.2
	}

	// 次要特征权重
	if fingerprint.Secondary.TextPattern != "" {
		confidence += 0.15
	}
	if fingerprint.Secondary.ResourceIDPattern != "" {
		confidence += 0.1
	}

	// 上下文权重
	if fingerprint.Context.ParentClass != "" {
		confidence += 0.05
	}

	return confidence
}

// findBestMatchWithStrategies 使用多种策略查找最佳匹配
func (ui *IntegratedUIService) findBestMatchWithStrategies(elements []ElementInfo, fingerprint EnhancedFingerprint, currentWidth, currentHeight int) *ElementInfo {
	// 策略1: 主要特征匹配
	if match := ui.matchByPrimaryFeatures(elements, fingerprint, currentWidth, currentHeight); match != nil {
		return match
	}

	// 策略2: 次要特征匹配
	if match := ui.matchBySecondaryFeatures(elements, fingerprint); match != nil {
		return match
	}

	// 策略3: 屏幕区域匹配
	if match := ui.matchByScreenRegion(elements, fingerprint, currentWidth, currentHeight); match != nil {
		return match
	}

	// 策略4: 兜底策略 - 绝对位置匹配
	if match := ui.matchByAbsolutePosition(elements, fingerprint, currentWidth, currentHeight); match != nil {
		return match
	}

	return nil
}

// matchByPrimaryFeatures 主要特征匹配
func (ui *IntegratedUIService) matchByPrimaryFeatures(elements []ElementInfo, fingerprint EnhancedFingerprint, currentWidth, currentHeight int) *ElementInfo {
	for _, element := range elements {
		score := 0.0

		// 类名匹配
		if fingerprint.Primary.Class != "" && element.Class == fingerprint.Primary.Class {
			score += 0.4
		}

		// 可点击性匹配
		if element.Clickable == fingerprint.Primary.Clickable {
			score += 0.2
		}

		// 位置百分比匹配（允许±10%偏差）
		if ui.isPositionMatch(element, fingerprint.Primary.PositionRatio, currentWidth, currentHeight, 0.1) {
			score += 0.3
		}

		// 尺寸匹配
		if ui.isSizeMatch(element, fingerprint.Primary.SizeRange) {
			score += 0.1
		}

		// 主要特征匹配阈值
		if score >= 0.7 {
			return &element
		}
	}

	return nil
}

// matchBySecondaryFeatures 次要特征匹配
func (ui *IntegratedUIService) matchBySecondaryFeatures(elements []ElementInfo, fingerprint EnhancedFingerprint) *ElementInfo {
	for _, element := range elements {
		// 文本模式匹配
		if fingerprint.Secondary.TextPattern != "" && element.Text != "" {
			if matched, _ := regexp.MatchString(fingerprint.Secondary.TextPattern, element.Text); matched {
				return &element
			}
		}

		// 资源ID模式匹配
		if fingerprint.Secondary.ResourceIDPattern != "" && element.ResourceID != "" {
			if matched, _ := regexp.MatchString(fingerprint.Secondary.ResourceIDPattern, element.ResourceID); matched {
				return &element
			}
		}

		// 内容描述模式匹配
		if fingerprint.Secondary.ContentDescPattern != "" && element.ContentDesc != "" {
			if matched, _ := regexp.MatchString(fingerprint.Secondary.ContentDescPattern, element.ContentDesc); matched {
				return &element
			}
		}
	}

	return nil
}

// matchByScreenRegion 屏幕区域匹配
func (ui *IntegratedUIService) matchByScreenRegion(elements []ElementInfo, fingerprint EnhancedFingerprint, currentWidth, currentHeight int) *ElementInfo {
	targetRegion := fingerprint.Primary.ScreenRegion

	for _, element := range elements {
		centerX := element.X + element.Width/2
		centerY := element.Y + element.Height/2
		elementRegion := ui.getScreenRegion(centerX, centerY, currentWidth, currentHeight)

		if elementRegion == targetRegion && element.Clickable {
			// 在同一区域内，优先选择尺寸相近的元素
			if ui.isSizeMatch(element, fingerprint.Primary.SizeRange) {
				return &element
			}
		}
	}

	// 如果没有尺寸匹配的，返回区域内第一个可点击元素
	for _, element := range elements {
		centerX := element.X + element.Width/2
		centerY := element.Y + element.Height/2
		elementRegion := ui.getScreenRegion(centerX, centerY, currentWidth, currentHeight)

		if elementRegion == targetRegion && element.Clickable {
			return &element
		}
	}

	return nil
}

// matchByAbsolutePosition 绝对位置匹配（兜底策略）
func (ui *IntegratedUIService) matchByAbsolutePosition(elements []ElementInfo, fingerprint EnhancedFingerprint, currentWidth, currentHeight int) *ElementInfo {
	if len(fingerprint.Fallback.AbsolutePos) == 0 {
		return nil
	}

	targetX := fingerprint.Fallback.AbsolutePos["x"]
	targetY := fingerprint.Fallback.AbsolutePos["y"]

	// 根据屏幕尺寸调整坐标
	scaleX := float64(currentWidth) / float64(fingerprint.Metadata.ScreenWidth)
	scaleY := float64(currentHeight) / float64(fingerprint.Metadata.ScreenHeight)

	adjustedX := int(float64(targetX) * scaleX)
	adjustedY := int(float64(targetY) * scaleY)

	// 查找最近的可点击元素
	var bestElement *ElementInfo
	minDistance := 100.0 // 最大允许距离

	for _, element := range elements {
		if !element.Clickable {
			continue
		}

		centerX := element.X + element.Width/2
		centerY := element.Y + element.Height/2

		dx := float64(centerX - adjustedX)
		dy := float64(centerY - adjustedY)
		distance := math.Sqrt(dx*dx + dy*dy)

		if distance < minDistance {
			minDistance = distance
			bestElement = &element
		}
	}

	return bestElement
}

// isPositionMatch 检查位置是否匹配
func (ui *IntegratedUIService) isPositionMatch(element ElementInfo, targetRatio map[string]float64, currentWidth, currentHeight int, tolerance float64) bool {
	centerX := element.X + element.Width/2
	centerY := element.Y + element.Height/2

	currentXRatio := float64(centerX) / float64(currentWidth)
	currentYRatio := float64(centerY) / float64(currentHeight)

	targetXRatio := targetRatio["x"]
	targetYRatio := targetRatio["y"]

	xDiff := math.Abs(currentXRatio - targetXRatio)
	yDiff := math.Abs(currentYRatio - targetYRatio)

	return xDiff <= tolerance && yDiff <= tolerance
}

// isSizeMatch 检查尺寸是否匹配
func (ui *IntegratedUIService) isSizeMatch(element ElementInfo, sizeRange map[string]int) bool {
	if len(sizeRange) == 0 {
		return true // 没有尺寸限制
	}

	widthMatch := true
	heightMatch := true

	if minWidth, ok := sizeRange["min_width"]; ok {
		widthMatch = widthMatch && element.Width >= minWidth
	}
	if maxWidth, ok := sizeRange["max_width"]; ok {
		widthMatch = widthMatch && element.Width <= maxWidth
	}
	if minHeight, ok := sizeRange["min_height"]; ok {
		heightMatch = heightMatch && element.Height >= minHeight
	}
	if maxHeight, ok := sizeRange["max_height"]; ok {
		heightMatch = heightMatch && element.Height <= maxHeight
	}

	return widthMatch && heightMatch
}

// findElementAtPosition 查找指定坐标处的UI元素
func (ui *IntegratedUIService) findElementAtPosition(xmlContent string, x, y int) *ElementInfo {
	var allElements []ElementInfo

	// 解析XML并获取所有元素
	var hierarchy Hierarchy
	if err := xml.Unmarshal([]byte(xmlContent), &hierarchy); err == nil {
		for _, node := range hierarchy.Nodes {
			ui.collectAllElements(&node, &allElements)
		}
	} else {
		var root Node
		if err := xml.Unmarshal([]byte(xmlContent), &root); err != nil {
			return nil
		}
		ui.collectAllElements(&root, &allElements)
	}

	// 查找包含指定坐标的候选元素
	var candidates []ElementInfo
	fmt.Printf("查找坐标(%d,%d)处的所有候选元素，总共解析到%d个UI元素:\n", x, y, len(allElements))

	// 统计不同类型的元素
	textElements := 0
	resourceIdElements := 0
	clickableElements := 0
	for _, element := range allElements {
		if element.Text != "" {
			textElements++
		}
		if element.ResourceID != "" {
			resourceIdElements++
		}
		if element.Clickable {
			clickableElements++
		}

		// 检查坐标是否在元素范围内
		if x >= element.X && x <= element.X+element.Width &&
			y >= element.Y && y <= element.Y+element.Height {
			candidates = append(candidates, element)
			fmt.Printf("  候选: text='%s', class='%s', resourceId='%s', bounds=(%d,%d,%d,%d), clickable=%t\n",
				element.Text, element.Class, element.ResourceID,
				element.X, element.Y, element.Width, element.Height, element.Clickable)
		}
	}

	fmt.Printf("UI元素统计: 总数=%d, 有文本=%d, 有资源ID=%d, 可点击=%d\n",
		len(allElements), textElements, resourceIdElements, clickableElements)

	if len(candidates) == 0 {
		return nil
	}

	// 简化选择策略：完全按照Python脚本的逻辑
	// 直接选择面积最小的元素（最精确匹配）
	var bestElement *ElementInfo
	minArea := math.MaxInt32

	fmt.Printf("从%d个候选元素中选择面积最小的:\n", len(candidates))
	for i, candidate := range candidates {
		area := candidate.Width * candidate.Height
		fmt.Printf("  候选%d: 面积=%d, text='%s', class='%s', resourceId='%s'\n",
			i+1, area, candidate.Text, candidate.Class, candidate.ResourceID)

		if area < minArea && area > 0 { // 确保面积大于0
			minArea = area
			bestElement = &candidate
		}
	}

	if bestElement != nil {
		fmt.Printf("选择了面积最小的元素: 面积=%d, text='%s', class='%s', resourceId='%s'\n",
			minArea, bestElement.Text, bestElement.Class, bestElement.ResourceID)
	}

	// 6. 特殊处理：如果找到的是应用图标但没有文本，尝试查找对应的文本标签
	if bestElement != nil && bestElement.Text == "" &&
		(bestElement.ResourceID == "com.miui.home:id/icon_icon" ||
			strings.Contains(bestElement.Class, "ImageView")) {

		// 查找同一区域内的文本标签
		for _, element := range allElements {
			if element.Text != "" &&
				strings.Contains(element.ResourceID, "icon_title") &&
				ui.isElementsNearby(*bestElement, element) {

				// 创建一个组合元素，包含图标的位置信息和文本标签的文本
				combinedElement := *bestElement
				combinedElement.Text = element.Text
				combinedElement.ContentDesc = element.ContentDesc

				fmt.Printf("找到对应的文本标签: '%s'\n", element.Text)
				return &combinedElement
			}
		}
	}

	return bestElement
}

// isElementsNearby 检查两个元素是否相邻（用于判断图标和文本标签的关系）
func (ui *IntegratedUIService) isElementsNearby(element1, element2 ElementInfo) bool {
	// 计算两个元素中心点的距离
	center1X := element1.X + element1.Width/2
	center1Y := element1.Y + element1.Height/2
	center2X := element2.X + element2.Width/2
	center2Y := element2.Y + element2.Height/2

	// 计算距离
	distanceX := math.Abs(float64(center1X - center2X))
	distanceY := math.Abs(float64(center1Y - center2Y))

	// 对于MIUI桌面图标，文本通常在图标下方，距离不超过图标高度的1.5倍
	maxDistance := float64(element1.Height) * 1.5

	// 检查是否在合理范围内（主要检查垂直距离，水平距离应该比较接近）
	return distanceX < float64(element1.Width) && distanceY < maxDistance
}
