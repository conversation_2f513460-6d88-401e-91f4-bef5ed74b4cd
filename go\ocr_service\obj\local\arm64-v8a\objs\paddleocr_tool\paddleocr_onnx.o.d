./obj/local/arm64-v8a/objs/paddleocr_tool/paddleocr_onnx.o: \
  jni/paddleocr_onnx.cpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/opencv.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/opencv_modules.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvdef.h \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/version.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/hal/interface.h \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cv_cpu_dispatch.h \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/base.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd_wrapper.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/neon_utils.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/vsx_utils.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/check.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/traits.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/matx.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/saturate.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/fast_math.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/matx.inl.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/types.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/mat.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/bufferpool.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/mat.inl.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/persistence.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/operations.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd.inl.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/utility.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/optim.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/features2d.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/highgui.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/highgui/highgui.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/imgproc.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/./imgproc/segmentation.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/photo.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video/tracking.hpp \
  jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video/background_segm.hpp \
  jni/include/onnxruntime/onnxruntime_cxx_api.h \
  jni/include/onnxruntime/onnxruntime_c_api.h \
  jni/include/onnxruntime/onnxruntime_float16.h \
  jni/include/onnxruntime/onnxruntime_cxx_inline.h \
  jni/../jni/clipper.hpp
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/opencv.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/opencv_modules.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvdef.h:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/version.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/hal/interface.h:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cv_cpu_dispatch.h:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/base.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd_wrapper.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/neon_utils.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/vsx_utils.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/check.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/traits.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/matx.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/saturate.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/fast_math.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/matx.inl.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/types.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/mat.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/bufferpool.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/mat.inl.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/persistence.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/operations.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/cvstd.inl.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/utility.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/core/optim.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/features2d.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/highgui.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/highgui/highgui.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/imgproc.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/./imgproc/segmentation.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/photo.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video/tracking.hpp:
jni/../libs/opencv-mobile-4.11.0-android/sdk/native/jni/include/opencv2/video/background_segm.hpp:
jni/include/onnxruntime/onnxruntime_cxx_api.h:
jni/include/onnxruntime/onnxruntime_c_api.h:
jni/include/onnxruntime/onnxruntime_float16.h:
jni/include/onnxruntime/onnxruntime_cxx_inline.h:
jni/../jni/clipper.hpp:
