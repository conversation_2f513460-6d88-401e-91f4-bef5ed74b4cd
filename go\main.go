package main

import (
	"log"
	"net/http"
	"os"
	"os/exec"
	"os/signal"
	"strings"
	"syscall"

	"ocr-server/internal/config"
	"ocr-server/internal/handlers"
	"ocr-server/internal/middleware"
	"ocr-server/internal/services"

	"github.com/gin-gonic/gin"
)

func main() {
	// 设置为release模式
	gin.SetMode(gin.ReleaseMode)

	// 初始化配置
	cfg := config.NewAppConfig()

	// 使用优化的集成服务
	log.Println("启动优化版本，使用集成OCR和UI服务...")

	// 初始化集成服务
	integratedOCRService := services.NewIntegratedOCRService(cfg)
	integratedUIService := services.NewIntegratedUIService(cfg)

	// 创建脚本服务，使用集成服务
	scriptService := services.NewScriptService(cfg, integratedOCRService, integratedUIService)

	// 初始化处理器
	screenshotHandler := handlers.NewScreenshotHandler(cfg)
	inputHandler := handlers.NewInputHandler(cfg)

	// 创建处理器，使用集成服务
	ocrHandler := handlers.NewOCRHandler(integratedOCRService)
	uiHandler := handlers.NewUIHandler(integratedUIService)
	scriptHandler := handlers.NewScriptHandler(scriptService)
	metricsHandler := handlers.NewMetricsHandler(integratedOCRService)

	// 设置Gin路由
	r := gin.Default()

	// 添加中间件
	r.Use(middleware.Logger())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.CORS())
	r.Use(middleware.RateLimiter())

	// 加载HTML模板
	r.LoadHTMLGlob("./templates/*")

	// 静态文件服务
	r.Static("/screenshots", "./"+cfg.ScreenshotDir)

	// 主页路由
	r.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "OCR自动化控制台 (优化版)",
		})
	})

	// 移动端页面路由
	r.GET("/mobile", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "移动端自动化控制台 (优化版)",
		})
	})

	// uiweb页面路由
	r.GET("/uiweb", func(c *gin.Context) {
		c.HTML(http.StatusOK, "index.html", gin.H{
			"title": "UI自动化控制台 (优化版)",
		})
	})

	// 设备信息API
	r.GET("/device/info", func(c *gin.Context) {
		deviceInfo := make(map[string]string)

		// 获取设备型号
		if output, err := exec.Command("getprop", "ro.product.model").Output(); err == nil {
			deviceInfo["model"] = strings.TrimSpace(string(output))
		} else {
			deviceInfo["model"] = "Unknown"
		}

		// 获取品牌
		if output, err := exec.Command("getprop", "ro.product.brand").Output(); err == nil {
			deviceInfo["brand"] = strings.TrimSpace(string(output))
		} else {
			deviceInfo["brand"] = "Unknown"
		}

		// 获取Android版本
		if output, err := exec.Command("getprop", "ro.build.version.release").Output(); err == nil {
			deviceInfo["android_version"] = "Android " + strings.TrimSpace(string(output))
		} else {
			deviceInfo["android_version"] = "Unknown"
		}

		// 获取SDK版本
		if output, err := exec.Command("getprop", "ro.build.version.sdk").Output(); err == nil {
			deviceInfo["sdk_version"] = strings.TrimSpace(string(output))
		} else {
			deviceInfo["sdk_version"] = "Unknown"
		}

		// 获取屏幕分辨率
		if output, err := exec.Command("wm", "size").Output(); err == nil {
			sizeStr := strings.TrimSpace(string(output))
			if strings.Contains(sizeStr, "Physical size:") {
				parts := strings.Split(sizeStr, "Physical size: ")
				if len(parts) > 1 {
					deviceInfo["resolution"] = strings.TrimSpace(parts[1])
				} else {
					deviceInfo["resolution"] = "Unknown"
				}
			} else {
				deviceInfo["resolution"] = sizeStr
			}
		} else {
			deviceInfo["resolution"] = "Unknown"
		}

		c.JSON(http.StatusOK, gin.H{
			"success":         true,
			"model":           deviceInfo["model"],
			"android_version": deviceInfo["android_version"],
			"resolution":      deviceInfo["resolution"],
			"brand":           deviceInfo["brand"],
			"sdk_version":     deviceInfo["sdk_version"],
		})
	})

	// 截图相关路由
	r.POST("/screenshot", screenshotHandler.HandleScreenshot)

	// 基础操作路由
	r.POST("/click", inputHandler.HandleClick)
	r.POST("/swipe", inputHandler.HandleSwipe)

	// OCR相关路由（使用优化服务）
	r.POST("/ocr", ocrHandler.HandleOCR)
	r.POST("/ocr-click", ocrHandler.HandleOCRClick)

	// UI自动化相关路由（使用优化服务）
	ui := r.Group("/ui")
	{
		ui.POST("/start-u2", uiHandler.HandleStartU2Service)
		ui.POST("/stop-u2", uiHandler.HandleStopU2Service)
		ui.GET("/service-status", uiHandler.HandleGetU2ServiceStatus)
		ui.GET("/dump", uiHandler.HandleGetUIHierarchy)
		ui.POST("/dump", uiHandler.HandleGetUIHierarchy) // 支持POST请求
		ui.POST("/match", uiHandler.HandleMatchUIElement)
		ui.POST("/match-fingerprint", uiHandler.HandleMatchUIElementByFingerprint)
		ui.POST("/fingerprint", uiHandler.HandleGenerateUIFingerprint)
		ui.POST("/enhanced-fingerprint", uiHandler.HandleGenerateEnhancedFingerprint)
		ui.GET("/elements", uiHandler.HandleGetAllUIElements)
	}

	// 脚本管理相关路由
	script := r.Group("/script")
	{
		script.POST("/start", scriptHandler.HandleStartScript)
		script.POST("/start-json", scriptHandler.HandleStartScriptFromJSON)
		script.POST("/stop", scriptHandler.HandleStopScript)
		script.GET("/status", scriptHandler.HandleGetScriptStatus)
		script.GET("/list", scriptHandler.HandleListAvailableScripts)
	}

	// API路由组
	api := r.Group("/api")
	{
		api.GET("/metrics", metricsHandler.HandleMetrics)
		api.GET("/health", metricsHandler.HandleHealthCheck)
	}

	// 性能监控路由
	r.GET("/performance", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":     "ok",
			"version":    "traditional",
			"ocr_mode":   "traditional",
			"ui_service": integratedUIService != nil,
			"message":    "传统模式运行中",
		})
	})

	// 健康检查路由（保持向后兼容）
	r.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "OCR自动化服务运行正常 (传统模式)",
		})
	})

	// 设置优雅关闭
	setupGracefulShutdown(integratedOCRService, integratedUIService)

	// 启动服务器
	log.Printf("OCR自动化服务启动 (优化版)，监听端口: %s", cfg.Port)
	log.Println("性能优化特性:")
	log.Println("- OCR常驻进程，避免重复加载模型")
	log.Println("- UI服务连接池，减少连接开销")
	log.Println("- 智能缓存，提高响应速度")

	if err := r.Run(":" + cfg.Port); err != nil {
		log.Fatalf("服务器启动失败: %v", err)
	}
}

// setupGracefulShutdown 设置优雅关闭
func setupGracefulShutdown(ocrService *services.IntegratedOCRService, uiService *services.IntegratedUIService) {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	go func() {
		<-c
		log.Println("正在关闭服务...")

		if ocrService != nil {
			log.Println("关闭OCR服务...")
			ocrService.Close()
		}

		if uiService != nil {
			log.Println("关闭UI服务...")
			uiService.Close()
		}

		log.Println("服务已关闭")
		os.Exit(0)
	}()
}
