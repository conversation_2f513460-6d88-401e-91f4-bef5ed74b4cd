package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"ocr-server/internal/models"
	"ocr-server/internal/services"
)

// ScriptHandler 脚本处理器
type ScriptHandler struct {
	scriptService *services.ScriptService
}

// NewScriptHandler 创建新的脚本处理器
func NewScriptHandler(scriptService *services.ScriptService) *ScriptHandler {
	return &ScriptHandler{
		scriptService: scriptService,
	}
}

// HandleStartScript 处理启动脚本请求
func (h *ScriptHandler) HandleStartScript(c *gin.Context) {
	var req models.StartScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	scriptID, err := h.scriptService.StartScript(req.ScriptPath)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"error": "启动脚本失败: " + err.Error(),
		})
		return
	}

	c.J<PERSON>(http.StatusOK, gin.H{
		"success":   true,
		"script_id": scriptID,
		"message":   "脚本启动成功",
	})
}

// HandleStartScriptFromConfig 处理从配置启动脚本请求
func (h *ScriptHandler) HandleStartScriptFromConfig(c *gin.Context) {
	var config interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	scriptID, err := h.scriptService.StartScriptFromConfig(&config)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "启动脚本失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"script_id": scriptID,
		"message":   "脚本启动成功",
	})
}

// HandleStopScript 处理停止脚本请求
func (h *ScriptHandler) HandleStopScript(c *gin.Context) {
	var req models.StopScriptRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "请求参数错误: " + err.Error(),
		})
		return
	}

	if err := h.scriptService.StopScript(req.ScriptID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "停止脚本失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "脚本已停止",
	})
}

// HandleGetScriptStatus 处理获取脚本状态请求
func (h *ScriptHandler) HandleGetScriptStatus(c *gin.Context) {
	scripts := h.scriptService.GetScriptStatus()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"scripts": scripts,
	})
}

// HandleStartScriptFromJSON 处理从JSON启动脚本请求
func (h *ScriptHandler) HandleStartScriptFromJSON(c *gin.Context) {
	var script interface{}
	if err := c.ShouldBindJSON(&script); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}

	// 从JSON启动脚本逻辑
	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "脚本启动成功",
		"script_id": "generated_script_id",
	})
}

// HandleListAvailableScripts 处理列出可用脚本请求
func (h *ScriptHandler) HandleListAvailableScripts(c *gin.Context) {
	scripts, err := h.scriptService.ListAvailableScripts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "获取脚本列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"scripts": scripts,
	})
}
