package utils

import (
	"bytes"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"ocr-server/internal/config"
)

// ScreenshotManager 截图管理器
type ScreenshotManager struct {
	config *config.AppConfig
}

// NewScreenshotManager 创建新的截图管理器
func NewScreenshotManager(cfg *config.AppConfig) *ScreenshotManager {
	return &ScreenshotManager{
		config: cfg,
	}
}

// GenerateFilename 生成截图文件名
func (sm *ScreenshotManager) GenerateFilename() string {
	timestamp := time.Now().Format(config.TimestampFormat)
	return fmt.Sprintf("%s%s%s", config.ScreenshotPrefix, timestamp, config.ScreenshotExt)
}

// TakeScreenshot 截图
func (sm *ScreenshotManager) TakeScreenshot(filename string) error {
	filepath := filepath.Join(sm.config.ScreenshotDir, filename)
	return sm.TakeScreenshotToPath(filepath)
}

// TakeScreenshotToPath 截图到指定路径
func (sm *ScreenshotManager) TakeScreenshotToPath(filepath string) error {
	// 使用优化的截图命令，减少文件I/O
	// -p 参数输出PNG格式，比默认格式更快
	// 添加超时控制，避免卡死
	cmd := exec.Command("screencap", "-p", filepath)

	// 设置超时，避免截图卡死
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("截图失败: %v", err)
	}
	return nil
}

// TakeScreenshotAndGetPath 截图并返回文件路径
func (sm *ScreenshotManager) TakeScreenshotAndGetPath() (string, error) {
	filename := sm.GenerateFilename()
	filepath := filepath.Join(sm.config.ScreenshotDir, filename)

	if err := sm.TakeScreenshotToPath(filepath); err != nil {
		return "", err
	}

	return filepath, nil
}

// TakeScreenshotToMemory 截图到内存（更快，避免文件I/O）
func (sm *ScreenshotManager) TakeScreenshotToMemory() ([]byte, error) {
	// 直接输出到stdout，避免文件I/O
	cmd := exec.Command("screencap", "-p")

	var stdout bytes.Buffer
	cmd.Stdout = &stdout

	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("内存截图失败: %v", err)
	}

	return stdout.Bytes(), nil
}

// TakeScreenshotFast 快速截图（优先使用内存模式）
func (sm *ScreenshotManager) TakeScreenshotFast() (string, error) {
	// 先尝试内存截图，然后写入文件
	data, err := sm.TakeScreenshotToMemory()
	if err != nil {
		// 回退到传统文件模式
		return sm.TakeScreenshotAndGetPath()
	}

	// 生成文件名并写入
	filename := sm.GenerateFilename()
	filepath := filepath.Join(sm.config.ScreenshotDir, filename)

	if err := os.WriteFile(filepath, data, 0644); err != nil {
		return "", fmt.Errorf("写入截图文件失败: %v", err)
	}

	return filepath, nil
}

// GetScreenshotDir 获取截图目录路径
func (sm *ScreenshotManager) GetScreenshotDir() string {
	return sm.config.ScreenshotDir
}
