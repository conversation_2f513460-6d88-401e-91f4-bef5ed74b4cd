# OCR搜索卡死问题修复

## 问题分析

### 为什么搜索会卡死而获取不会？

#### 1. **协议复杂性差异**
```
获取(OCR): 
- 发送: "OCR STDIN"
- 接收: 简单的JSON响应

搜索(SEARCH):
- 发送: "SEARCH text STDIN" 
- 接收: 可能需要特殊格式的响应
- 处理: 需要额外的文本匹配逻辑
```

#### 2. **常驻进程的搜索协议问题**
- 常驻进程可能不支持`SEARCH`命令
- 搜索需要的数据格式可能与普通OCR不同
- 响应格式可能不是标准的一行文本

#### 3. **死锁风险**
```go
// 问题代码：可能的死锁
for i := 0; i < ocr.maxRetries; i++ {
    result, err = ocr.SearchText("", text)  // 调用SearchText
    // SearchText内部调用callOCRDaemonMemory
    // callOCRDaemonMemory获取ocrMutex锁
    // 如果进程卡死，锁永远不会释放
}
```

## 修复方案

### 1. **简化搜索逻辑**
```go
// 修复前：复杂的常驻进程搜索
func (ocr *IntegratedOCRService) SearchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
    // 使用内存模式搜索 - 可能卡死
    response, err := ocr.callOCRDaemonMemory(fmt.Sprintf("SEARCH %s", searchText))
}

// 修复后：直接使用传统模式
func (ocr *IntegratedOCRService) SearchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
    // 直接使用传统模式，避免常驻进程的搜索协议问题
    if imagePath == "" {
        imagePath, err = ocr.screenshotManager.TakeScreenshotFast()
    }
    return ocr.searchText(imagePath, searchText)
}
```

### 2. **添加超时控制**
```go
// 为传统搜索添加超时机制
func (ocr *IntegratedOCRService) searchText(imagePath, searchText string) (*models.OCRSearchResult, error) {
    cmd := exec.Command(ocr.config.PaddleOCRTool, imagePath, "--search", searchText)
    
    // 添加超时控制
    done := make(chan error, 1)
    go func() {
        output, err = cmd.Output()
        done <- err
    }()
    
    select {
    case err := <-done:
        // 正常完成
    case <-time.After(ocr.requestTimeout):
        cmd.Process.Kill()
        return nil, fmt.Errorf("OCR搜索超时")
    }
}
```

### 3. **移除重试死锁**
```go
// 修复前：可能导致死锁的重试
for i := 0; i < ocr.maxRetries; i++ {
    result, err = ocr.SearchText("", text)  // 可能卡死
}

// 修复后：直接调用，避免复杂重试
result, err := ocr.SearchText("", text)
```

## 性能对比

| 方法 | 响应时间 | 稳定性 | 卡死风险 | 适用场景 |
|------|----------|--------|----------|----------|
| 常驻进程OCR | 100-200ms | 高 | 低 | 频繁的文本识别 |
| 常驻进程搜索 | 未知 | 低 | **高** | ❌ 不推荐 |
| 传统模式搜索 | 500-1000ms | 高 | 低 | 文本搜索和点击 |

## 建议的使用策略

### 1. **功能分离**
```
OCR识别: 使用常驻进程 (快速、稳定)
文本搜索: 使用传统模式 (稳定、可靠)
```

### 2. **优化建议**
- **OCR获取**: 继续使用常驻进程，性能优异
- **文本搜索**: 使用传统模式，避免协议复杂性
- **混合策略**: 根据操作类型选择最适合的方法

### 3. **监控要点**
```bash
# 检查搜索性能
curl -X POST http://localhost:9088/ocr-click \
  -H "Content-Type: application/json" \
  -d '{"text": "测试文字"}'

# 监控响应时间
curl http://localhost:9088/api/metrics
```

## 故障排除

### 1. **如果搜索仍然卡死**
```bash
# 检查OCR工具是否支持搜索
./paddleocr_tool test.png --search "测试"

# 检查进程状态
ps aux | grep paddleocr

# 检查日志
tail -f /var/log/ocr-server.log
```

### 2. **性能调优**
```go
// 调整超时时间
service := &IntegratedOCRService{
    requestTimeout: 10 * time.Second,  // 搜索超时
    daemonTimeout:  5 * time.Second,   // 进程启动超时
}
```

### 3. **降级策略**
如果搜索功能仍有问题，可以完全禁用常驻进程搜索：
```go
// 配置选项
type AppConfig struct {
    DisableDaemonSearch bool `json:"disable_daemon_search"`
}
```

## 总结

修复的核心思路：
1. **简化搜索逻辑** - 避免复杂的常驻进程协议
2. **添加超时控制** - 防止无限等待
3. **功能分离** - OCR用常驻进程，搜索用传统模式
4. **移除死锁风险** - 简化重试逻辑

这样既保持了OCR的高性能，又确保了搜索功能的稳定性。
