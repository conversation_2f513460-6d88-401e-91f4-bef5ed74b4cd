@echo off
echo Pushing binary files to phone...

REM Push executable file
adb push libs/arm64-v8a/paddleocr_tool /data/local/tmp/

REM Push dependency libraries
adb push libs/arm64-v8a/libonnxruntime.so /data/local/tmp/
adb push libs/arm64-v8a/libc++_shared.so /data/local/tmp/

REM Set executable permissions
adb shell chmod 755 /data/local/tmp/paddleocr_tool
adb shell chmod 755 /data/local/tmp/libonnxruntime.so
adb shell chmod 755 /data/local/tmp/libc++_shared.so

REM Push model files
adb push models/ch_PP-OCRv4_det_infer.onnx /data/local/tmp/
adb push models/ch_PP-OCRv4_rec_infer.onnx /data/local/tmp/
adb push models/ppocr_keys_v1.txt /data/local/tmp/

REM Push test image
adb push models/1.png /data/local/tmp/

echo Push completed!
echo Now you can run test on phone
adb shell "cd /data/local/tmp && ./1.sh 1.png"
