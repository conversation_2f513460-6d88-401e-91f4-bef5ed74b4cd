﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <Keyword>Win32Proj</Keyword>
    <ProjectName>clipper_test</ProjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>C:\Program Files (x86)\Borland\agg-2.5\include;C:\Program Files (x86)\Borland\agg-2.5\include\platform;C:\Program Files (x86)\Borland\agg-2.5\include\platform\win32;C:\Program Files (x86)\Borland\agg-2.5\include\ctrl;C:\Program Files (x86)\Borland\agg-2.5\include\util;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>true</LinkIncremental>
    <IncludePath>C:\Program Files (x86)\Borland\agg-2.5\include;C:\Program Files (x86)\Borland\agg-2.5\include\platform;C:\Program Files (x86)\Borland\agg-2.5\include\platform\win32;C:\Program Files (x86)\Borland\agg-2.5\include\ctrl;C:\Program Files (x86)\Borland\agg-2.5\include\util;$(IncludePath)</IncludePath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <WarningLevel>Level3</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
    </ClCompile>
    <Link>
      <TargetMachine>MachineX86</TargetMachine>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\agg_src\examples\make_arrows.cpp" />
    <ClCompile Include="..\agg_src\examples\make_gb_poly.cpp" />
    <ClCompile Include="..\agg_src\src\agg_arc.cpp" />
    <ClCompile Include="..\agg_src\src\agg_arrowhead.cpp" />
    <ClCompile Include="..\agg_src\src\agg_bezier_arc.cpp" />
    <ClCompile Include="..\agg_src\src\agg_bspline.cpp" />
    <ClCompile Include="..\agg_src\src\agg_curves.cpp" />
    <ClCompile Include="..\agg_src\src\agg_embedded_raster_fonts.cpp" />
    <ClCompile Include="..\agg_src\src\agg_gsv_text.cpp" />
    <ClCompile Include="..\agg_src\src\agg_image_filters.cpp" />
    <ClCompile Include="..\agg_src\src\agg_line_aa_basics.cpp" />
    <ClCompile Include="..\agg_src\src\agg_line_profile_aa.cpp" />
    <ClCompile Include="..\agg_src\src\agg_rounded_rect.cpp" />
    <ClCompile Include="..\agg_src\src\agg_sqrt_tables.cpp" />
    <ClCompile Include="..\agg_src\src\agg_trans_affine.cpp" />
    <ClCompile Include="..\agg_src\src\agg_trans_double_path.cpp" />
    <ClCompile Include="..\agg_src\src\agg_trans_single_path.cpp" />
    <ClCompile Include="..\agg_src\src\agg_trans_warp_magnifier.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_bspline.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_contour.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_dash.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_markers_term.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_smooth_poly1.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vcgen_stroke.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vpgen_clip_polygon.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vpgen_clip_polyline.cpp" />
    <ClCompile Include="..\agg_src\src\agg_vpgen_segmentator.cpp" />
    <ClCompile Include="..\agg_src\src\ctrl\agg_rbox_ctrl.cpp" />
    <ClCompile Include="..\agg_src\src\platform\win32\agg_platform_support.cpp" />
    <ClCompile Include="..\agg_src\src\platform\win32\agg_win32_bmp.cpp" />
    <ClCompile Include="..\clipper.cpp" />
    <ClCompile Include="clipper_test.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\clipper.hpp" />
    <ClInclude Include="agg_conv_clipper.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>