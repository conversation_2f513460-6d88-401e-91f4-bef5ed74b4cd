<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            width: 100%;
            height: 100vh;
            margin: 0;
            background: white;
            box-sizing: border-box;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 1em;
            opacity: 0.9;
        }

        .nav-links {
            margin-top: 15px;
            display: flex;
            justify-content: center;
            gap: 20px;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.6);
        }

        .nav-link.active {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.8);
        }
        
        .content {
            padding: 0;
            display: flex;
            gap: 20px;
            height: 100%;
            align-items: flex-start;
        }
        
        .left-panel {
            flex: 0 0 auto;
            min-width: 400px;
            display: flex;
            flex-direction: column;
            gap: 15px;
            height: calc(100vh - 120px);
        }
        
        .middle-panel {
            flex: 1;
            min-width: 350px;
            max-width: 400px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 18px;
            border: 1px solid #e9ecef;
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
            align-self: flex-start;
        }
        
        .right-panel {
            flex: 0 0 auto;
            min-width: 400px;
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
            gap: 15px;
            overflow-y: auto;
        }
        
        /* 标签页样式 */
        .tabs {
            display: flex;
            border-bottom: 2px solid #e0e0e0;
            margin-bottom: 20px;
        }
        
        .tab {
            padding: 12px 24px;
            background-color: #f8f9fa;
            border: none;
            cursor: pointer;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            margin-right: 4px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .tab:hover {
            background-color: #e9ecef;
        }
        
        .tab.active {
            background-color: #007bff;
            color: white;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .screenshot-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 18px;
            border: 1px solid #e9ecef;
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .operations-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 18px;
            border: 1px solid #e9ecef;
            height: 35%;
            overflow-y: auto;
        }
        
        .function-panel {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            border: 1px solid #e9ecef;
            margin-bottom: 15px;
        }
        
        .function-panel h4 {
            color: #495057;
            margin-bottom: 12px;
            font-size: 1.1em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .compact-form {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            align-items: end;
            flex-wrap: wrap;
            max-width: 100%;
        }
        
        .compact-form .form-group {
            margin-bottom: 0;
            flex: 1;
            min-width: 80px;
            max-width: 120px;
        }
        
        .compact-form label {
            font-size: 0.9em;
            margin-bottom: 4px;
        }
        
        .compact-form .form-control {
            padding: 8px 12px;
            font-size: 14px;
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 14px;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 18px;
            border: 1px solid #e9ecef;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 18px rgba(0,0,0,0.1);
        }
        
        .card h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card h3::before {
            content: "📱";
            margin-right: 10px;
            font-size: 1.2em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        
        .result {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            display: none;
        }
        
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        .screenshot-container {
            display: inline-block;
            padding: 15px;
            background-color: #ffffff;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        .screenshot-container img {
            max-width: 500px;
            max-height: 80vh;
            width: auto;
            height: auto;
            display: block;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            object-fit: contain;
            cursor: pointer;
        }
        
        .screenshot-container .placeholder {
            color: #888;
            font-style: italic;
            padding: 40px 20px;
            font-size: 16px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        /* 弹窗样式 */
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #333;
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1000;
            max-width: 400px;
            word-wrap: break-word;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }
        
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .toast.success {
            background-color: #28a745;
        }
        
        .toast.error {
            background-color: #dc3545;
        }
        
        .toast.info {
            background-color: #17a2b8;
        }
        

        
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            align-items: center;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .feature-list li::before {
            content: "✅";
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 OCR自动化控制台</h1>
            <p>智能OCR识别 · UI自动化 · 脚本执行 · 一站式自动化解决方案</p>
            <div class="nav-links">
                <a href="/" class="nav-link active">🏠 控制台</a>
            </div>
        </div>

        <div class="content">
            <!-- 左侧面板：截图显示区域 -->
            <div class="left-panel">
                <!-- 截图显示区域 -->
                <div class="screenshot-panel">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <h3 style="margin: 0;">📱 设备屏幕</h3>
                        <div>
                            <button class="btn btn-small" onclick="takeScreenshot()">📸 截图</button>
                        </div>
                    </div>
                    <div class="loading" id="screenshotLoading">
                        <div class="spinner"></div>
                        <p>正在截图...</p>
                    </div>
                    <div class="screenshot-container" id="screenshotContainer" style="flex: 1; display: flex; align-items: center; justify-content: center; border: 2px dashed #ddd; border-radius: 10px; background: #fff;">
                        <p style="color: #999; text-align: center;">点击"截图"查看设备屏幕<br>截图后可直接点击图片进行操作</p>
                    </div>
                </div>
            </div>
            
            <!-- 中间面板：操作结果显示 -->
            <div class="middle-panel">
                <h3 style="margin-bottom: 15px;">📋 识别结果</h3>
                <div id="resultDisplay" style="background: white; border: 2px solid #007bff; border-radius: 8px; padding: 15px; flex: 1; overflow-y: auto; font-size: 14px; line-height: 1.5; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <p style="color: #999; text-align: center; margin: 0; font-weight: bold;">📋 OCR和UI识别结果将显示在这里</p>
                    <p style="color: #666; text-align: center; margin: 5px 0 0 0; font-size: 12px;">点击OCR按钮开始识别</p>
                </div>
            </div>
                
            <!-- 右侧面板：功能操作 -->
            <div class="right-panel">
                <!-- 标签页导航 -->
                <div class="tabs">
                    <button class="tab active" onclick="switchTab('ocr-tab')">🔍 OCR识别</button>
                    <button class="tab" onclick="switchTab('ui-tab')">🌳 UI操作</button>
                    <button class="tab" onclick="switchTab('control-tab')">⚡ 控制操作</button>
                    <button class="tab" onclick="switchTab('script-tab')">🤖 脚本管理</button>
                </div>
                
                <!-- OCR标签页内容 -->
                <div id="ocr-tab" class="tab-content active">
                    <!-- OCR功能 -->
                    <div class="function-panel">
                        <h4>🔍 OCR识别</h4>
                        <div style="margin-bottom: 15px;">
                            <button class="btn btn-success btn-small" onclick="performOCR()" style="width: 100%;">🔍 OCR识别</button>
                        </div>
                        <div class="compact-form">
                            <div class="form-group">
                                <label>查找文字:</label>
                                <input type="text" class="form-control" id="targetText" placeholder="例如: 确定">
                            </div>
                            <button class="btn btn-success btn-small" onclick="performOCRClick()">识别并点击</button>
                        </div>
                        <div class="loading" id="ocrLoading">
                            <div class="spinner"></div>
                            <p>正在识别文字...</p>
                        </div>
                        <div class="loading" id="ocrClickLoading">
                            <div class="spinner"></div>
                            <p>正在识别并点击...</p>
                        </div>
                    </div>
                    

                </div>
                
                <!-- UI操作标签页内容 -->
                <div id="ui-tab" class="tab-content">
                    <!-- U2服务管理 -->
                    <div class="function-panel">
                        <h4>🔧 U2服务</h4>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                            <button class="btn btn-small" onclick="startU2Service()">启动</button>
                            <button class="btn btn-secondary btn-small" onclick="stopU2Service()">停止</button>
                            <button class="btn btn-success btn-small" onclick="checkU2Status()">状态</button>
                        </div>
                    </div>
                    
                    <!-- UI操作 -->
                    <div class="function-panel">
                        <h4>🌳 UI操作</h4>
                        <div class="compact-form" style="margin-bottom: 10px;">
                            <button class="btn btn-small" onclick="getAllUIElements()">获取所有元素</button>
                        </div>
                        <div class="compact-form">
                            <div class="form-group">
                                <label>匹配文本:</label>
                                <input type="text" class="form-control" id="matchText" placeholder="例如: 设置">
                            </div>
                            <button class="btn btn-primary btn-small" onclick="matchAndClickUIElement()">匹配并点击</button>
                        </div>
                        <div class="loading" id="elementsLoading">
                            <div class="spinner"></div>
                            <p>正在获取UI元素...</p>
                        </div>
                    </div>
                    
                    <!-- 指纹匹配功能 -->
                    <div class="function-panel">
                        <h4>🔍 指纹匹配点击</h4>
                        <div class="form-group">
                            <label>指纹数据:</label>
                            <textarea class="form-control" id="fingerprintData" rows="2" placeholder="请粘贴要匹配的指纹JSON数据"></textarea>
                        </div>
                        <div class="compact-form">
                            <button class="btn btn-primary btn-small" onclick="matchAndClickFingerprint()">匹配并点击</button>
                        </div>
                        <div class="loading" id="fingerprintLoading">
                            <div class="spinner"></div>
                            <p>正在处理指纹...</p>
                        </div>
                    </div>
                </div>
                
                <!-- 控制操作标签页内容 -->
                <div id="control-tab" class="tab-content">
                    <!-- 点击功能 -->
                    <div class="function-panel">
                        <h4>👆 点击操作</h4>
                        <div class="compact-form">
                            <div class="form-group">
                                <label>X坐标:</label>
                                <input type="number" class="form-control" id="clickX" placeholder="540">
                            </div>
                            <div class="form-group">
                                <label>Y坐标:</label>
                                <input type="number" class="form-control" id="clickY" placeholder="960">
                            </div>
                            <button class="btn btn-small" onclick="performClick()">执行点击</button>
                        </div>
                    </div>

                    <!-- 滑动功能 -->
                    <div class="function-panel">
                        <h4>👋 滑动操作</h4>
                        <div class="compact-form">
                            <div class="form-group">
                                <label>起始X:</label>
                                <input type="number" class="form-control" id="swipeX1" placeholder="起始X">
                            </div>
                            <div class="form-group">
                                <label>起始Y:</label>
                                <input type="number" class="form-control" id="swipeY1" placeholder="起始Y">
                            </div>
                            <div class="form-group">
                                <label>结束X:</label>
                                <input type="number" class="form-control" id="swipeX2" placeholder="结束X">
                            </div>
                        </div>
                        <div class="compact-form">
                            <div class="form-group">
                                <label>结束Y:</label>
                                <input type="number" class="form-control" id="swipeY2" placeholder="结束Y">
                            </div>
                            <div class="form-group">
                                <label>时长(ms):</label>
                                <input type="number" class="form-control" id="swipeDuration" placeholder="300" value="300">
                            </div>
                            <button class="btn btn-small" onclick="performSwipe()">执行滑动</button>
                        </div>
                    </div>

                    <!-- 通用操作 -->
                    <div class="function-panel">
                        <h4>🛠️ 通用操作</h4>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-secondary btn-small" onclick="clearResults()">🗑️ 清空结果</button>
                        </div>
                    </div>
                </div>

                <!-- 脚本管理标签页内容 -->
                <div id="script-tab" class="tab-content">
                    <!-- 脚本列表 -->
                    <div class="function-panel">
                        <h4>📋 脚本列表</h4>
                        <div style="margin-bottom: 10px;">
                            <button class="btn btn-success btn-small" onclick="loadScriptList()">🔄 刷新列表</button>
                        </div>
                        <div id="scriptList" style="max-height: 200px; overflow-y: auto; background: white; border: 1px solid #ddd; border-radius: 5px; padding: 10px;">
                            <p style="color: #999; text-align: center;">点击刷新列表查看可用脚本</p>
                        </div>
                        <div class="loading" id="scriptListLoading">
                            <div class="spinner"></div>
                            <p>正在加载脚本列表...</p>
                        </div>
                    </div>

                    <!-- 脚本控制 -->
                    <div class="function-panel">
                        <h4>🎮 脚本控制</h4>
                        <div class="form-group">
                            <label>脚本路径:</label>
                            <input type="text" class="form-control" id="scriptPath" placeholder="scripts/example_script.json">
                        </div>
                        <div class="form-group">
                            <label>或直接粘贴脚本JSON:</label>
                            <textarea class="form-control" id="scriptJsonInput" placeholder="粘贴完整的脚本JSON内容..." rows="4"></textarea>
                        </div>
                        <div style="display: flex; gap: 5px; flex-wrap: wrap; margin-bottom: 10px;">
                            <button class="btn btn-success btn-small" onclick="startScript()">▶️ 启动脚本</button>
                            <button class="btn btn-primary btn-small" onclick="startScriptFromJson()">🚀 运行JSON脚本</button>
                            <button class="btn btn-secondary btn-small" onclick="stopScript()">⏹️ 停止脚本</button>
                            <button class="btn btn-small" onclick="getScriptStatus()">📊 查看状态</button>
                        </div>
                        <div class="loading" id="scriptControlLoading">
                            <div class="spinner"></div>
                            <p>正在处理脚本操作...</p>
                        </div>
                    </div>

                    <!-- 运行状态 -->
                    <div class="function-panel">
                        <h4>📈 运行状态</h4>
                        <div id="scriptStatus" style="background: white; border: 1px solid #ddd; border-radius: 5px; padding: 10px; min-height: 100px;">
                            <p style="color: #999; text-align: center;">脚本状态信息将显示在这里</p>
                        </div>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-small" onclick="autoRefreshStatus()">🔄 自动刷新</button>
                            <span id="autoRefreshIndicator" style="margin-left: 10px; color: #666; font-size: 12px;"></span>
                        </div>
                    </div>

                    <!-- 运行日志 -->
                    <div class="function-panel">
                        <h4>📝 运行日志</h4>
                        <div id="scriptLogs" style="background: #1e1e1e; color: #00ff00; border: 1px solid #ddd; border-radius: 5px; padding: 10px; min-height: 200px; max-height: 300px; overflow-y: auto; font-family: 'Courier New', monospace; font-size: 12px; white-space: pre-wrap;">
                            <p style="color: #888; text-align: center;">运行日志将显示在这里</p>
                        </div>
                        <div style="margin-top: 10px;">
                            <button class="btn btn-small" onclick="clearLogs()">🗑️ 清空日志</button>
                            <button class="btn btn-small" onclick="autoRefreshLogs()">🔄 自动刷新日志</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 显示结果
        function showResult(elementId, message, isSuccess = true) {
            console.log('showResult called with elementId:', elementId, 'message:', message, 'isSuccess:', isSuccess);
            // 如果是OCR或UI相关结果，显示在结果框中
            if (elementId.includes('ocr') || elementId.includes('hierarchy') || elementId.includes('elements') || elementId.includes('match')) {
                console.log('Calling showResultInBox for OCR/UI result');
                showResultInBox(message, isSuccess);
            } else {
                console.log('Calling showToast for other result');
                // 其他结果仍使用弹窗
                showToast(message, isSuccess ? 'success' : 'error');
            }
        }
        
        function clearResultBox() {
            const resultDisplay = document.getElementById('resultDisplay');
            if (resultDisplay) {
                resultDisplay.innerHTML = '<p style="text-align: center; color: #999; margin: 20px 0;">等待操作结果...</p>';
            }
        }
        
        function showResultInBox(message, isSuccess = true) {
            console.log('showResultInBox called with:', message, isSuccess);
            const resultDisplay = document.getElementById('resultDisplay');
            console.log('resultDisplay element:', resultDisplay);
            const timestamp = new Date().toLocaleTimeString();
            const statusIcon = isSuccess ? '✅' : '❌';
            const statusColor = isSuccess ? '#28a745' : '#dc3545';
            
            // 创建新的结果项
            const resultItem = document.createElement('div');
            resultItem.style.cssText = `
                border-left: 4px solid ${statusColor};
                padding: 10px;
                margin-bottom: 10px;
                background: ${isSuccess ? '#f8fff9' : '#fff8f8'};
                border-radius: 4px;
            `;
            
            resultItem.innerHTML = `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                    <span style="font-weight: bold; color: ${statusColor};">${statusIcon} ${isSuccess ? '识别成功' : '识别失败'}</span>
                    <span style="font-size: 12px; color: #666;">${timestamp}</span>
                </div>
                <div style="color: #333;">${message}</div>
            `;
            
            // 清空默认提示文字
            if (resultDisplay.querySelector('p[style*="color: #999"]')) {
                resultDisplay.innerHTML = '';
            }
            
            // 添加新结果到顶部
            resultDisplay.insertBefore(resultItem, resultDisplay.firstChild);
            
            // 限制显示的结果数量，保留最新的5条
            const items = resultDisplay.children;
            while (items.length > 5) {
                resultDisplay.removeChild(items[items.length - 1]);
            }
            
            // 滚动到顶部显示最新结果
            resultDisplay.scrollTop = 0;
        }
        
        function showToast(message, type = 'info', duration = 3000) {
            // 创建弹窗元素
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            
            // 添加到页面
            document.body.appendChild(toast);
            
            // 显示动画
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            // 自动消失
            setTimeout(() => {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, duration);
        }
        
        // 显示加载状态
        function showLoading(elementId, show = true) {
            const element = document.getElementById(elementId);
            if (element) {
                element.style.display = show ? 'block' : 'none';
            } else {
                console.warn(`Loading element not found: ${elementId}`);
            }
        }
        
        // 截图功能
        async function takeScreenshot() {
            showLoading('screenshotLoading', true);
            try {
                const response = await fetch('/screenshot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('screenshotResult', `截图成功！文件: ${result.filename}`);
                    document.getElementById('screenshotContainer').innerHTML = 
                         `<img src="${result.url}" alt="截图" onclick="clickOnImage(event)" data-url="${result.url}" id="screenshotImage">`;
                } else {
                    showResult('screenshotResult', `截图失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('screenshotResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('screenshotLoading', false);
            }
        }
        
        // 点击功能
        async function performClick() {
            const x = parseInt(document.getElementById('clickX').value);
            const y = parseInt(document.getElementById('clickY').value);
            
            if (isNaN(x) || isNaN(y)) {
                showResult('clickResult', '请输入有效的坐标', false);
                return;
            }
            
            try {
                const response = await fetch('/click', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ x, y })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('clickResult', result.message);
                } else {
                    showResult('clickResult', `点击失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('clickResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // 滑动功能
        async function performSwipe() {
            const x1 = parseInt(document.getElementById('swipeX1').value);
            const y1 = parseInt(document.getElementById('swipeY1').value);
            const x2 = parseInt(document.getElementById('swipeX2').value);
            const y2 = parseInt(document.getElementById('swipeY2').value);
            const duration = parseInt(document.getElementById('swipeDuration').value) || 300;
            
            if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {
                showResult('swipeResult', '请输入有效的坐标', false);
                return;
            }
            
            try {
                const response = await fetch('/swipe', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ x1, y1, x2, y2, duration })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('swipeResult', result.message);
                } else {
                    showResult('swipeResult', `滑动失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('swipeResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // OCR识别
        async function performOCR() {
            clearResultBox();
            showLoading('ocrLoading', true);
            try {
                const response = await fetch('/ocr', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ method: 'paddleocr' })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const responseText = await response.text();
                if (!responseText.trim()) {
                    throw new Error('服务器返回空响应');
                }
                
                const result = JSON.parse(responseText);
                
                if (result.success) {
                    let message = `<div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">`;
                    message += `<h4 style="color: #28a745; margin-bottom: 10px;">🎉 OCR识别成功！</h4>`;
                    message += `<p><strong>识别方法:</strong> PaddleOCR 原始输出</p>`;
                    
                    // 显示OCR识别结果
                    if (result.text && Array.isArray(result.text) && result.text.length > 0) {
                        message += `<div style="margin: 10px 0;"><strong>识别结果:</strong></div>`;
                        message += `<div style="background: white; padding: 12px; border-radius: 5px; border: 1px solid #ddd;">`;

                        result.text.forEach((item, index) => {
                            if (item && item.text) {
                                message += `<div style="margin-bottom: 8px; padding: 8px; border-left: 3px solid #007bff; background: #f8f9fa;">`;
                                message += `<strong>${index + 1}.</strong> ${item.text}`;
                                if (item.confidence) {
                                    message += ` <span style="color: #6c757d; font-size: 0.9em;">(置信度: ${(item.confidence * 100).toFixed(1)}%)</span>`;
                                }
                                if (item.bounds) {
                                    message += `<br><small style="color: #6c757d;">坐标: (${item.bounds.x}, ${item.bounds.y}) 大小: ${item.bounds.width}×${item.bounds.height}</small>`;
                                }
                                message += `</div>`;
                            }
                        });

                        message += `</div>`;
                    } else if (typeof result.text === 'string' && result.text) {
                        // 兼容字符串格式
                        message += `<div style="margin: 10px 0;"><strong>识别结果:</strong></div>`;
                        message += `<div style="background: white; padding: 12px; border-radius: 5px; border: 1px solid #ddd; font-family: monospace; white-space: pre-wrap;">${result.text}</div>`;
                    } else {
                        message += `<div style="color: #6c757d;">未识别到文字内容</div>`;
                    }
                    
                    message += `</div>`;
                    
                    showResult('ocrResult', message);
                } else {
                    showResult('ocrResult', `识别失败: ${result.message || result.error}`, false);
                }
            } catch (error) {
                showResult('ocrResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('ocrLoading', false);
            }
        }
        
        // OCR识别并点击
        async function performOCRClick() {
            console.log('performOCRClick started');
            clearResultBox();
            const text = document.getElementById('targetText').value.trim();
            
            if (!text) {
                showResult('ocrClickResult', '请输入要查找的文字', false);
                return;
            }
            
            console.log('Starting OCR click for text:', text);
            showLoading('ocrClickLoading', true);
            try {
                const response = await fetch('/ocr-click', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ text, method: 'paddleocr' })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const responseText = await response.text();
                if (!responseText.trim()) {
                    throw new Error('服务器返回空响应');
                }
                
                const result = JSON.parse(responseText);
                
                if (result.success) {
                    let message = `<strong>${result.message}</strong><br>`;
                    message += `<strong>点击坐标:</strong> (${result.click_x}, ${result.click_y})<br>`;
                    if (result.ocr_result && result.ocr_result.text) {
                        message += `<strong>完整识别结果:</strong><br>`;
                        message += `<div style="background: white; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ddd;">`;

                        if (Array.isArray(result.ocr_result.text)) {
                            result.ocr_result.text.forEach((item, index) => {
                                if (item && item.text) {
                                    message += `<div style="margin-bottom: 5px;"><strong>${index + 1}.</strong> ${item.text}`;
                                    if (item.confidence) {
                                        message += ` <span style="color: #6c757d; font-size: 0.9em;">(${(item.confidence * 100).toFixed(1)}%)</span>`;
                                    }
                                    message += `</div>`;
                                }
                            });
                        } else {
                            message += result.ocr_result.text;
                        }

                        message += `</div>`;
                    }
                    

                    
                    showResult('ocrClickResult', message);
                } else {
                    let message = `识别或点击失败: ${result.error}`;
                    if (result.ocr_result) {
                        message += `<br><strong>OCR识别结果:</strong><br><div style="background: white; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ddd;">${result.ocr_result}</div>`;
                    }
                    showResult('ocrClickResult', message, false);
                }
            } catch (error) {
                console.error('OCR click error:', error);
                showResult('ocrClickResult', `请求失败: ${error.message}`, false);
            } finally {
                console.log('OCR click finished, hiding loading');
                showLoading('ocrClickLoading', false);
            }
        }
        
        // U2服务管理功能
        async function startU2Service() {
            try {
                const response = await fetch('/ui/start-u2', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('u2ServiceResult', `U2服务启动成功: ${result.message}`);
                } else {
                    showResult('u2ServiceResult', `启动失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('u2ServiceResult', `请求失败: ${error.message}`, false);
            }
        }
        
        async function stopU2Service() {
            try {
                const response = await fetch('/ui/stop-u2', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('u2ServiceResult', `U2服务停止成功: ${result.message}`);
                } else {
                    showResult('u2ServiceResult', `停止失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('u2ServiceResult', `请求失败: ${error.message}`, false);
            }
        }
        
        async function checkU2Status() {
            try {
                const response = await fetch('/ui/service-status', {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('u2ServiceResult', `U2服务状态: ${result.message}`);
                } else {
                    showResult('u2ServiceResult', `状态检查失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('u2ServiceResult', `请求失败: ${error.message}`, false);
            }
        }
        
        // UI层次结构功能
        async function getUIHierarchy() {
            showLoading('hierarchyLoading', true);
            try {
                const response = await fetch('/ui/dump', {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let message = `<strong>UI层次结构获取成功</strong><br>`;
                    message += `<div style="background: white; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;"><pre>${result.result}</pre></div>`;
                    showResult('hierarchyResult', message);
                } else {
                    showResult('hierarchyResult', `获取失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('hierarchyResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('hierarchyLoading', false);
            }
        }
        
        // UI元素匹配并点击功能
        async function matchAndClickUIElement() {
            // 清空结果框
            clearResultBox();
            
            const text = document.getElementById('matchText').value.trim();
            
            if (!text) {
                showResultInBox('请输入要匹配的文本', false);
                return;
            }
            
            try {
                // 先匹配元素
                const matchResponse = await fetch('/ui/match', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ template: { text: text } })
                });
                
                const matchResult = await matchResponse.json();
                
                if (matchResult.success && matchResult.result) {
                    // 如果匹配成功，获取坐标并点击
                    const element = matchResult.result;
                    // bounds可能在element.bounds或element.element.bounds中
                    const bounds = element.bounds || (element.element && element.element.bounds);
                    if (bounds) {
                        // 调试信息：显示bounds的实际值
                        console.log('Bounds value:', bounds, 'Type:', typeof bounds);
                        console.log('Full element:', element);
                        
                        // 解析bounds字符串格式 "[x1,y1][x2,y2]"
                        const boundsMatch = bounds.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                        console.log('Bounds match result:', boundsMatch);
                        
                        if (boundsMatch) {
                            const x1 = parseInt(boundsMatch[1]);
                            const y1 = parseInt(boundsMatch[2]);
                            const x2 = parseInt(boundsMatch[3]);
                            const y2 = parseInt(boundsMatch[4]);
                            console.log('Parsed coordinates:', {x1, y1, x2, y2});
                            
                            const centerX = Math.round((x1 + x2) / 2);
                            const centerY = Math.round((y1 + y2) / 2);
                            console.log('Center coordinates:', {centerX, centerY});
                        
                            // 执行点击
                            const clickResponse = await fetch('/click', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ x: centerX, y: centerY })
                            });
                            
                            const clickResult = await clickResponse.json();
                            
                            if (clickResult.success) {
                                showResultInBox(`<strong>匹配并点击成功！</strong><br>文本: ${text}<br>坐标: (${centerX}, ${centerY})<br>边界: ${bounds}`);
                            } else {
                                showResultInBox(`匹配成功但点击失败: ${clickResult.error}`, false);
                            }
                        } else {
                            showResultInBox(`匹配成功但无法解析元素坐标<br>Bounds值: ${bounds}`, false);
                        }
                    } else {
                        showResultInBox('匹配成功但无法获取元素坐标', false);
                    }
                } else {
                    showResultInBox(`未找到匹配的文本: ${text}`, false);
                }
            } catch (error) {
                showResultInBox(`请求失败: ${error.message}`, false);
            }
        }
        
        // UI指纹匹配并点击功能
        async function matchAndClickFingerprint() {
            // 清空结果框
            clearResultBox();
            
            const fingerprintData = document.getElementById('fingerprintData').value.trim();
            
            if (!fingerprintData) {
                showResultInBox('请输入指纹数据', false);
                return;
            }
            
            showLoading('fingerprintLoading', true);
            
            try {
                // 先匹配指纹
                const matchResponse = await fetch('/ui/match-fingerprint', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ fingerprint: fingerprintData })
                });
                
                const matchResult = await matchResponse.json();
                
                if (matchResult.success && matchResult.result) {
                    // 如果匹配成功，获取坐标并点击
                    const element = matchResult.result;
                    // bounds可能在element.bounds或element.element.bounds中
                    const bounds = element.bounds || (element.element && element.element.bounds);
                    if (bounds) {
                        // 调试信息：显示bounds的实际值
                        console.log('Fingerprint Bounds value:', bounds, 'Type:', typeof bounds);
                        console.log('Fingerprint Full element:', element);
                        
                        // 解析bounds字符串格式 "[x1,y1][x2,y2]"
                        const boundsMatch = bounds.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                        console.log('Fingerprint Bounds match result:', boundsMatch);
                        
                        if (boundsMatch) {
                            const x1 = parseInt(boundsMatch[1]);
                            const y1 = parseInt(boundsMatch[2]);
                            const x2 = parseInt(boundsMatch[3]);
                            const y2 = parseInt(boundsMatch[4]);
                            console.log('Fingerprint Parsed coordinates:', {x1, y1, x2, y2});
                            
                            const centerX = Math.round((x1 + x2) / 2);
                            const centerY = Math.round((y1 + y2) / 2);
                            console.log('Fingerprint Center coordinates:', {centerX, centerY});
                        
                            // 执行点击
                            const clickResponse = await fetch('/click', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ x: centerX, y: centerY })
                            });
                            
                            const clickResult = await clickResponse.json();
                            
                            if (clickResult.success) {
                                showResultInBox(`<strong>指纹匹配并点击成功！</strong><br>坐标: (${centerX}, ${centerY})<br>边界: ${bounds}`);
                            } else {
                                showResultInBox(`指纹匹配成功但点击失败: ${clickResult.error}`, false);
                            }
                        } else {
                            showResultInBox(`指纹匹配成功但无法解析元素坐标<br>Bounds值: ${bounds}`, false);
                        }
                    } else {
                        showResultInBox('指纹匹配成功但无法获取元素坐标', false);
                    }
                } else {
                    showResultInBox(`指纹匹配失败: ${matchResult.error || '未找到匹配的元素'}`, false);
                }
            } catch (error) {
                showResultInBox(`请求失败: ${error.message}`, false);
            } finally {
                showLoading('fingerprintLoading', false);
            }
        }
        

        
        // 获取所有UI元素
        async function getAllUIElements() {
            showLoading('elementsLoading', true);
            try {
                const response = await fetch('/ui/elements', {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let message = `<strong>UI元素获取成功！</strong><br>`;
                    if (result.result) {
                        message += `<div style="background: white; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;"><pre>${JSON.stringify(result.result, null, 2)}</pre></div>`;
                        

                    } else {
                        message += '未找到UI元素';
                    }
                    showResult('elementsResult', message);
                } else {
                    showResult('elementsResult', `获取失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('elementsResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('elementsLoading', false);
            }
        }
        
        // 脚本模板功能
        async function validateScript() {
            const scriptContent = document.getElementById('scriptTemplate').value.trim();
            
            if (!scriptContent) {
                showResult('scriptResult', '请输入脚本内容', false);
                return;
            }
            
            try {
                const script = JSON.parse(scriptContent);
                
                const response = await fetch('/script/validate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ script })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showResult('scriptResult', '<strong>脚本验证通过！</strong><br>脚本格式正确，可以执行。');
                } else {
                    showResult('scriptResult', `脚本验证失败: ${result.error}`, false);
                }
            } catch (error) {
                if (error instanceof SyntaxError) {
                    showResult('scriptResult', `JSON格式错误: ${error.message}`, false);
                } else {
                    showResult('scriptResult', `验证失败: ${error.message}`, false);
                }
            }
        }
        
        async function executeScript() {
            const scriptContent = document.getElementById('scriptTemplate').value.trim();
            
            if (!scriptContent) {
                showResult('scriptResult', '请输入脚本内容', false);
                return;
            }
            
            try {
                const script = JSON.parse(scriptContent);
                
                showLoading('scriptLoading', true);
                document.getElementById('scriptResult').style.display = 'none';
                document.getElementById('scriptScreenshots').innerHTML = '';
                
                const response = await fetch('/script/execute', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ script })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    let message = `<strong>脚本执行完成！</strong><br>`;
                    message += `<div style="margin-top: 10px;"><strong>执行结果:</strong></div>`;
                    
                    if (result.results && result.results.length > 0) {
                        message += '<div style="background: white; padding: 10px; border-radius: 5px; margin-top: 10px; border: 1px solid #ddd; max-height: 300px; overflow-y: auto;">';
                        result.results.forEach((actionResult, index) => {
                            const status = actionResult.success ? '✅' : '❌';
                            message += `<div style="margin-bottom: 10px; padding: 8px; border-left: 3px solid ${actionResult.success ? '#28a745' : '#dc3545'}; background: ${actionResult.success ? '#f8fff9' : '#fff8f8'};"><strong>${status} ${actionResult.action_name}</strong><br>`;
                            if (actionResult.message) {
                                message += `消息: ${actionResult.message}<br>`;
                            }
                            if (actionResult.error) {
                                message += `错误: ${actionResult.error}<br>`;
                            }
                            message += `时间: ${actionResult.timestamp}</div>`;
                        });
                        message += '</div>';
                    }
                    
                    // 显示截图
                    if (result.screenshots && result.screenshots.length > 0) {
                        let screenshotHtml = '<div style="margin-top: 15px;"><strong>执行截图:</strong></div><div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px;">';
                        result.screenshots.forEach((screenshot, index) => {
                            screenshotHtml += `<div style="text-align: center;"><img src="${screenshot}" alt="截图${index + 1}" style="max-width: 100%; max-height: 200px; border-radius: 5px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); cursor: pointer;" onclick="window.open('${screenshot}', '_blank')"><div style="margin-top: 5px; font-size: 12px; color: #666;">截图 ${index + 1}</div></div>`;
                        });
                        screenshotHtml += '</div>';
                        document.getElementById('scriptScreenshots').innerHTML = screenshotHtml;
                    }
                    
                    showResult('scriptResult', message);
                } else {
                    showResult('scriptResult', `脚本执行失败: ${result.error}`, false);
                }
            } catch (error) {
                if (error instanceof SyntaxError) {
                    showResult('scriptResult', `JSON格式错误: ${error.message}`, false);
                } else {
                    showResult('scriptResult', `执行失败: ${error.message}`, false);
                }
            } finally {
                showLoading('scriptLoading', false);
            }
        }
        
        function loadExampleScript() {
            const exampleScript = {
                "name": "UI指纹匹配示例",
                "description": "演示如何使用UI指纹进行元素匹配和点击",
                "version": "1.0",
                "settings": {
                    "timeout": 30,
                    "stop_on_error": true,
                    "take_screenshot": true
                },
                "actions": [
                    {
                        "type": "screenshot",
                        "name": "初始截图",
                        "params": {},
                        "wait_after": 1000
                    },
                    {
                        "type": "ui_match_fingerprint",
                        "name": "匹配并点击目标元素",
                        "params": {
                            "fingerprint": {
                                "hash": "557e2d45",
                                "score": 85,
                                "id": "bin.mt.plus:id/file_name",
                                "text": "ocr_service",
                                "class": "android.widget.TextView",
                                "pos": {"x": 0.683, "y": 0.204, "w": 0.178, "h": 0.02},
                                "parent_class": "android.view.ViewGroup",
                                "states": {"enabled": true}
                            },
                            "click": true
                        },
                        "retry_count": 3,
                        "wait_after": 2000
                    },
                    {
                        "type": "screenshot",
                        "name": "操作后截图",
                        "params": {},
                        "wait_after": 500
                    }
                ]
            };

            document.getElementById('scriptTemplate').value = JSON.stringify(exampleScript, null, 2);
            showResult('scriptResult', '示例脚本已加载，您可以直接执行或根据需要修改。');
        }

        // 脚本管理功能
        let autoRefreshTimer = null;
        let currentScriptId = null;

        // 加载脚本列表
        async function loadScriptList() {
            showLoading('scriptListLoading', true);
            try {
                const response = await fetch('/script/list', {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.success) {
                    const scriptListDiv = document.getElementById('scriptList');
                    if (result.scripts && result.scripts.length > 0) {
                        let html = '<div style="font-weight: bold; margin-bottom: 10px; color: #28a745;">📋 可用脚本列表:</div>';
                        result.scripts.forEach(script => {
                            html += `<div style="padding: 8px; margin-bottom: 5px; background: #f8f9fa; border-radius: 5px; border-left: 3px solid #007bff; cursor: pointer;" onclick="selectScript('${script}')">`;
                            html += `<div style="font-weight: bold; color: #007bff;">${script}</div>`;
                            html += `<div style="font-size: 12px; color: #666;">点击选择此脚本</div>`;
                            html += `</div>`;
                        });
                        scriptListDiv.innerHTML = html;
                    } else {
                        scriptListDiv.innerHTML = '<p style="color: #999; text-align: center;">未找到可用脚本</p>';
                    }
                } else {
                    showResult('scriptListResult', `加载脚本列表失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('scriptListResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('scriptListLoading', false);
            }
        }

        // 选择脚本
        function selectScript(scriptPath) {
            document.getElementById('scriptPath').value = scriptPath;
            showToast(`已选择脚本: ${scriptPath}`, 'info', 2000);
        }

        // 启动脚本
        async function startScript() {
            const scriptPath = document.getElementById('scriptPath').value.trim();

            if (!scriptPath) {
                showResult('scriptControlResult', '请输入脚本路径', false);
                return;
            }

            showLoading('scriptControlLoading', true);
            try {
                const response = await fetch('/script/start', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ script_path: scriptPath })
                });

                const result = await response.json();

                if (result.success) {
                    currentScriptId = result.script_id;
                    showResult('scriptControlResult', `脚本启动成功！<br>脚本ID: ${result.script_id}<br>脚本路径: ${scriptPath}`);
                    // 自动刷新状态
                    setTimeout(() => getScriptStatus(), 1000);
                } else {
                    showResult('scriptControlResult', `脚本启动失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('scriptControlResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('scriptControlLoading', false);
            }
        }

        // 停止脚本
        async function stopScript() {
            if (!currentScriptId) {
                showResult('scriptControlResult', '没有正在运行的脚本', false);
                return;
            }

            showLoading('scriptControlLoading', true);
            try {
                const response = await fetch('/script/stop', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ script_id: currentScriptId })
                });

                const result = await response.json();

                if (result.success) {
                    showResult('scriptControlResult', `脚本停止成功！<br>脚本ID: ${currentScriptId}`);
                    currentScriptId = null;
                    // 停止自动刷新
                    if (autoRefreshTimer) {
                        clearInterval(autoRefreshTimer);
                        autoRefreshTimer = null;
                        document.getElementById('autoRefreshIndicator').textContent = '';
                    }
                    // 刷新状态
                    setTimeout(() => getScriptStatus(), 1000);
                } else {
                    showResult('scriptControlResult', `脚本停止失败: ${result.error}`, false);
                }
            } catch (error) {
                showResult('scriptControlResult', `请求失败: ${error.message}`, false);
            } finally {
                showLoading('scriptControlLoading', false);
            }
        }

        // 获取脚本状态
        async function getScriptStatus() {
            try {
                const response = await fetch('/script/status', {
                    method: 'GET'
                });

                const result = await response.json();

                if (result.success) {
                    const statusDiv = document.getElementById('scriptStatus');
                    const timestamp = new Date().toLocaleTimeString();

                    let html = `<div style="margin-bottom: 10px;"><strong>📊 脚本状态</strong> <span style="font-size: 12px; color: #666;">(${timestamp})</span></div>`;

                    if (result.running_scripts && result.running_scripts.length > 0) {
                        html += '<div style="color: #28a745; font-weight: bold; margin-bottom: 10px;">🟢 运行中的脚本:</div>';
                        result.running_scripts.forEach(script => {
                            const runTime = script.runtime ? `运行时间: ${Math.floor(script.runtime / 1000)}秒` : '';
                            html += `<div style="background: #f8fff9; border-left: 3px solid #28a745; padding: 10px; margin-bottom: 8px; border-radius: 5px;">`;
                            html += `<div style="font-weight: bold;">ID: ${script.script_id}</div>`;
                            html += `<div>名称: ${script.name || '未知'}</div>`;
                            html += `<div>状态: ${script.status}</div>`;
                            if (runTime) html += `<div>${runTime}</div>`;
                            if (script.current_flow) html += `<div>当前流程: ${script.current_flow}</div>`;
                            html += `</div>`;
                        });
                    } else {
                        html += '<div style="color: #6c757d; text-align: center; padding: 20px;">🔴 当前没有运行中的脚本</div>';
                    }

                    statusDiv.innerHTML = html;
                } else {
                    document.getElementById('scriptStatus').innerHTML = `<p style="color: #dc3545;">状态获取失败: ${result.error}</p>`;
                }
            } catch (error) {
                document.getElementById('scriptStatus').innerHTML = `<p style="color: #dc3545;">请求失败: ${error.message}</p>`;
            }
        }

        // 自动刷新状态
        function autoRefreshStatus() {
            if (autoRefreshTimer) {
                // 停止自动刷新
                clearInterval(autoRefreshTimer);
                autoRefreshTimer = null;
                document.getElementById('autoRefreshIndicator').textContent = '';
                showToast('已停止自动刷新', 'info', 1500);
            } else {
                // 开始自动刷新
                autoRefreshTimer = setInterval(() => {
                    getScriptStatus();
                }, 3000);
                document.getElementById('autoRefreshIndicator').textContent = '🔄 每3秒自动刷新';
                showToast('已开启自动刷新 (每3秒)', 'success', 2000);
                // 立即刷新一次
                getScriptStatus();
            }
        }

        // 从JSON运行脚本
        async function startScriptFromJson() {
            const jsonContent = document.getElementById('scriptJsonInput').value.trim();
            if (!jsonContent) {
                showToast('请粘贴脚本JSON内容', 'error');
                return;
            }

            try {
                // 验证JSON格式
                const scriptData = JSON.parse(jsonContent);
                // 支持新的紧凑型格式 (n: name) 和旧格式 (name)
                if (!scriptData.n && !scriptData.name) {
                    showToast('脚本格式不正确，缺少脚本名称', 'error');
                    return;
                }

                showLoading('scriptControlLoading', true);

                const response = await fetch('/script/start-json', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: jsonContent
                });

                const result = await response.json();

                if (result.success) {
                    currentScriptId = result.script_id;
                    const scriptName = scriptData.n || scriptData.name;
                    showToast(`脚本启动成功: ${scriptName}`, 'success');
                    // 开始自动刷新状态
                    if (!autoRefreshTimer) {
                        autoRefreshStatus();
                    }
                } else {
                    showToast(`脚本启动失败: ${result.error}`, 'error');
                }
            } catch (error) {
                showToast(`JSON格式错误: ${error.message}`, 'error');
            } finally {
                showLoading('scriptControlLoading', false);
            }
        }

        // 清空日志
        function clearLogs() {
            document.getElementById('scriptLogs').innerHTML = '<p style="color: #888; text-align: center;">运行日志将显示在这里</p>';
            showToast('日志已清空', 'info', 1500);
        }

        // 自动刷新日志
        let logRefreshTimer = null;
        function autoRefreshLogs() {
            if (logRefreshTimer) {
                clearInterval(logRefreshTimer);
                logRefreshTimer = null;
                showToast('已停止自动刷新日志', 'info', 1500);
            } else {
                logRefreshTimer = setInterval(() => {
                    fetchScriptLogs();
                }, 2000);
                showToast('已开启日志自动刷新 (每2秒)', 'success', 2000);
                fetchScriptLogs();
            }
        }

        // 获取脚本日志
        async function fetchScriptLogs() {
            if (!currentScriptId) return;

            try {
                const response = await fetch(`/script/logs/${currentScriptId}`);
                const result = await response.json();

                if (result.success && result.logs) {
                    const logsDiv = document.getElementById('scriptLogs');
                    logsDiv.innerHTML = result.logs.map(log => {
                        const timestamp = new Date(log.timestamp).toLocaleTimeString();
                        const level = log.level || 'INFO';
                        const color = level === 'ERROR' ? '#ff6b6b' : level === 'WARN' ? '#feca57' : '#00ff00';
                        return `<div style="color: ${color};">[${timestamp}] ${level}: ${log.message}</div>`;
                    }).join('');

                    // 自动滚动到底部
                    logsDiv.scrollTop = logsDiv.scrollHeight;
                }
            } catch (error) {
                console.error('获取日志失败:', error);
            }
        }

        // 点击图片功能
        function clickOnImage(event) {
            const img = event.target;
            const rect = img.getBoundingClientRect();
            const x = Math.round((event.clientX - rect.left) * (img.naturalWidth / img.offsetWidth));
            const y = Math.round((event.clientY - rect.top) * (img.naturalHeight / img.offsetHeight));
            
            document.getElementById('clickX').value = x;
            document.getElementById('clickY').value = y;
            
            showResult('clickResult', `已设置点击坐标: (${x}, ${y})`);
        }
        

        
        // 清空所有结果
         function clearResults() {
             showToast('已清空所有结果', 'info', 1500);
         }
        
        // 标签页切换功能
        function switchTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active状态
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabId).classList.add('active');
            
            // 设置对应标签为active状态
            event.target.classList.add('active');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 可以在这里添加一些初始化代码
            console.log('Android OCR 和 UI自动化服务界面已加载');
        });
    </script>
</body>
</html>