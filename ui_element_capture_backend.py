import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import xml.etree.ElementTree as ET
import threading
import time
import requests
from PIL import Image, ImageTk, ImageDraw, ImageFont
import io
import re


class UIElementCaptureBackend:
    def __init__(self, root):
        self.root = root
        self.root.title("📱 手机UI元素捕捉工具 (后端API版本)")
        
        # 设置窗口大小
        self._setup_window_geometry()
        
        # 变量初始化
        self._initialize_variables()
        
        # 设置UI
        self.setup_ui()
        
        # 默认自动连接后端
        threading.Thread(target=self._async_backend_initialization, daemon=True).start()
    
    def _setup_window_geometry(self):
        """设置窗口几何属性"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        size_ratio = 0.8
        window_width = min(int(screen_width * size_ratio), 1200)
        window_height = min(int(screen_height * size_ratio), 700)
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
    
    def _initialize_variables(self):
        """初始化所有变量"""
        # 后端API配置
        self.api_base = "http://localhost:9088"  # 默认后端地址，端口固定9088
        self.is_backend_connected = False

        # 界面相关变量
        self.screenshot_label = None
        self.current_screenshot = None
        self.ui_hierarchy = None
        self.scale_factor = 1.0
        self.current_element = None
        self.marked_coordinates = []

        # 搜索相关变量
        self.search_matches = []
        self.search_text_var = None
        self.search_entry = None
        self._last_fingerprint_match_scores = []

        # OCR相关变量
        self.ocr_results = []
        self.show_ocr = None
        self.ocr_text_display = None
    
    def _async_backend_initialization(self):
        """异步后端初始化"""
        if self.is_backend_connected:  # 防止重复连接
            return

        max_retries = 3
        for attempt in range(max_retries):
            try:
                print(f"尝试连接后端 (第{attempt + 1}次)...")

                # 更新状态
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(
                        text="🔄 正在连接后端...", foreground="orange"))

                # 测试后端连接
                response = requests.get(f"{self.api_base}/ui/service-status", timeout=5)
                if response.status_code == 200:
                    result = response.json()
                    print(f"后端连接成功: {result}")

                    self.is_backend_connected = True

                    # 更新UI状态
                    if hasattr(self, 'status_label') and self.status_label is not None:
                        self.root.after(0, lambda: self.status_label.config(
                            text="✅ 后端已连接", foreground="green"))
                    if hasattr(self, 'refresh_btn') and self.refresh_btn is not None:
                        self.root.after(0, lambda: self.refresh_btn.config(state="normal"))

                    # 自动截图
                    self.root.after(1000, self.capture_screenshot)
                    return  # 成功连接，退出重试循环
                else:
                    raise Exception(f"后端响应错误: {response.status_code}")
                
            except Exception as e:
                error_msg = str(e)
                print(f"连接失败 (第{attempt + 1}次): {error_msg}")
                
                if attempt == max_retries - 1:  # 最后一次尝试
                    if hasattr(self, 'status_label') and self.status_label is not None:
                        self.root.after(0, lambda: self.status_label.config(
                            text=f"❌ 连接错误: {error_msg}", foreground="red"))
                    self.is_backend_connected = False
                else:
                    time.sleep(3)  # 等待3秒后重试
    
    def manual_backend_connection(self):
        """手动连接后端（用于重连按钮）"""
        # 更新API地址，端口固定9088
        ip_value = self.ip_var.get().strip()
        if ip_value:
            if ip_value.startswith('http'):
                # 如果用户输入了完整URL，提取IP部分
                ip_value = ip_value.replace('http://', '').replace('https://', '').split(':')[0]
            self.api_base = f"http://{ip_value}:9088"

        self.is_backend_connected = False
        threading.Thread(target=self._async_backend_initialization, daemon=True).start()
    
    def capture_screenshot(self):
        """捕获截图和UI层次结构"""
        def capture():
            try:
                if not self.is_backend_connected:
                    self.root.after(0, lambda: messagebox.showerror("错误", "后端未连接"))
                    return
                
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(text="📸 正在截图...", foreground="blue"))
                
                # 调用后端截图API
                response = requests.post(f"{self.api_base}/screenshot", 
                                       json={}, timeout=10)
                if response.status_code != 200:
                    raise Exception(f"截图请求失败: {response.status_code}")
                
                result = response.json()
                if not result.get('success'):
                    raise Exception(result.get('error', '截图失败'))
                
                # 下载截图
                screenshot_url = f"{self.api_base}/screenshots/{result['filename']}"
                img_response = requests.get(screenshot_url, timeout=10)
                if img_response.status_code != 200:
                    raise Exception("下载截图失败")
                
                # 加载截图
                screenshot_data = Image.open(io.BytesIO(img_response.content))
                self.current_screenshot = screenshot_data
                
                # 获取UI层次结构
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(text="🔍 获取UI结构...", foreground="blue"))
                ui_success = self.dump_ui_hierarchy()
                
                if ui_success:
                    # 计算缩放比例
                    canvas_width = self.screenshot_canvas.winfo_width() or 400
                    canvas_height = self.screenshot_canvas.winfo_height() or 600
                    
                    img_width, img_height = screenshot_data.size
                    scale_x = (canvas_width - 20) / img_width
                    scale_y = (canvas_height - 20) / img_height
                    self.scale_factor = min(scale_x, scale_y, 1.0)
                    
                    new_width = int(img_width * self.scale_factor)
                    new_height = int(img_height * self.scale_factor)
                    
                    resized_image = screenshot_data.resize((new_width, new_height), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(resized_image)
                    
                    self.root.after(0, lambda: self.display_screenshot(photo))
                    
                    if hasattr(self, 'status_label') and self.status_label is not None:
                        self.root.after(0, lambda: self.status_label.config(
                            text="✅ 截图和UI结构获取完成", foreground="green"))
                else:
                    self.current_screenshot = None
                    self.ui_hierarchy = None
                    self.root.after(0, lambda: self.clear_screenshot_display())
                    if hasattr(self, 'status_label') and self.status_label is not None:
                        self.root.after(0, lambda: self.status_label.config(
                            text="❌ UI结构获取失败，请重试", foreground="red"))
                    
            except Exception as e:
                error_msg = str(e)
                self.root.after(0, lambda: messagebox.showerror("错误", f"截图出错: {error_msg}"))
        
        threading.Thread(target=capture, daemon=True).start()
    
    def dump_ui_hierarchy(self):
        """获取UI层次结构"""
        try:
            # 调用后端UI层级API
            response = requests.get(f"{self.api_base}/ui/dump", timeout=10)
            if response.status_code != 200:
                print(f"UI层级请求失败: {response.status_code}")
                return False
            
            result = response.json()
            if not result.get('success'):
                print(f"UI层级获取失败: {result.get('error', '未知错误')}")
                return False
            
            xml_content = result.get('result', '')
            if not xml_content or '<' not in xml_content:
                print("UI层级XML内容为空或无效")
                return False
            
            try:
                hierarchy = ET.fromstring(xml_content)
                element_count = len(hierarchy.findall('.//*'))
                print(f"✅ UI层次结构获取成功，共 {element_count} 个元素")
                
                self.ui_hierarchy = hierarchy
                return True
                
            except ET.ParseError as e:
                print(f"XML解析失败: {e}")
                return False
                
        except Exception as e:
            print(f"获取UI层级失败: {e}")
            return False
    
    def clear_screenshot_display(self):
        """清空截图显示"""
        if self.screenshot_label:
            self.screenshot_label.configure(image='')
            self.screenshot_label.image = None

    def setup_ui(self):
        """设置用户界面"""
        # 创建主容器
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 左侧面板 - 截图显示
        left_frame = ttk.LabelFrame(main_paned, text="📱 手机屏幕 (点击获取元素)", padding=5)
        main_paned.add(left_frame, weight=2)

        # 控制按钮
        control_frame = ttk.Frame(left_frame)
        control_frame.pack(fill=tk.X, pady=(0, 5))

        # 第一行按钮
        button_row1 = ttk.Frame(control_frame)
        button_row1.pack(fill=tk.X, pady=(0, 2))

        # IP输入框
        ip_frame = ttk.Frame(button_row1)
        ip_frame.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Label(ip_frame, text="后端IP:").pack(side=tk.LEFT)
        self.ip_var = tk.StringVar(value="**************")  # 默认IP设置
        self.ip_entry = ttk.Entry(ip_frame, textvariable=self.ip_var, width=15)
        self.ip_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 连接按钮
        self.connect_btn = ttk.Button(button_row1, text="🔗 连接",
                                     command=self.manual_backend_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=(5, 10))

        # 刷新按钮
        self.refresh_btn = ttk.Button(button_row1, text="🔄 刷新截图",
                                     command=self.capture_screenshot, state="disabled")
        self.refresh_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 状态标签
        self.status_label = ttk.Label(button_row1, text="📱 正在初始化...", foreground="orange")
        self.status_label.pack(side=tk.LEFT)

        # 第二行选项
        button_row2 = ttk.Frame(control_frame)
        button_row2.pack(fill=tk.X)

        # 显示边框切换按钮
        self.show_bounds = tk.BooleanVar(value=True)
        self.bounds_btn = ttk.Checkbutton(button_row2, text="📦 显示控件边框",
                                         variable=self.show_bounds,
                                         command=self.toggle_bounds_display)
        self.bounds_btn.pack(side=tk.LEFT, padx=(0, 10))

        # OCR边框切换按钮
        self.show_ocr = tk.BooleanVar(value=False)
        self.ocr_btn = ttk.Checkbutton(button_row2, text="📝 OCR边框",
                                      variable=self.show_ocr,
                                      command=self.toggle_ocr_display)
        self.ocr_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 截图显示区域
        screenshot_container = ttk.Frame(left_frame)
        screenshot_container.pack(fill=tk.BOTH, expand=True)

        self.screenshot_canvas = tk.Canvas(screenshot_container, bg='white')
        v_scrollbar = ttk.Scrollbar(screenshot_container, orient=tk.VERTICAL, command=self.screenshot_canvas.yview)
        h_scrollbar = ttk.Scrollbar(screenshot_container, orient=tk.HORIZONTAL, command=self.screenshot_canvas.xview)

        self.screenshot_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
        self.screenshot_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        self.screenshot_frame = ttk.Frame(self.screenshot_canvas)
        self.canvas_frame_id = self.screenshot_canvas.create_window(0, 0, anchor="nw", window=self.screenshot_frame)

        self.screenshot_canvas.bind('<Configure>', self.on_canvas_configure)
        self.screenshot_canvas.bind("<MouseWheel>", self.on_mousewheel)

        # 右侧面板 - 元素信息
        right_frame = ttk.LabelFrame(main_paned, text="📋 元素信息", padding=5)
        main_paned.add(right_frame, weight=1)

        # 坐标显示
        coord_frame = ttk.Frame(right_frame)
        coord_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(coord_frame, text="🎯 点击坐标:").pack(side=tk.LEFT)
        self.coord_label = ttk.Label(coord_frame, text="未点击", foreground="gray")
        self.coord_label.pack(side=tk.LEFT, padx=(10, 0))

        # 信息显示区域 - 使用标签页
        self.info_notebook = ttk.Notebook(right_frame)
        self.info_notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # UI元素信息标签页
        ui_frame = ttk.Frame(self.info_notebook)
        self.info_notebook.add(ui_frame, text="📋 UI元素信息")

        self.info_text = scrolledtext.ScrolledText(
            ui_frame,
            width=50, height=20,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.info_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # OCR文字信息标签页
        ocr_frame = ttk.Frame(self.info_notebook)
        self.info_notebook.add(ocr_frame, text="📝 OCR文字")

        self.ocr_text_display = scrolledtext.ScrolledText(
            ocr_frame,
            width=50, height=20,
            font=('Consolas', 9),
            wrap=tk.WORD
        )
        self.ocr_text_display.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 为了兼容性，保留其他引用
        self.node_info_text = self.info_text
        self.fingerprint_info_text = self.info_text

        # 智能搜索功能
        search_frame = ttk.LabelFrame(right_frame, text="🔍 智能搜索", padding=5)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        # 搜索输入框
        search_input_frame = tk.Frame(search_frame, bg='#f0f0f0')
        search_input_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(search_input_frame, text="搜索内容:", bg='#f0f0f0').pack(side=tk.LEFT)

        # 创建搜索文本变量
        self.search_text_var = tk.StringVar()

        # 使用StringVar创建输入框
        self.search_entry = tk.Entry(search_input_frame, width=25,
                                   textvariable=self.search_text_var,
                                   font=('Arial', 10),
                                   bg='white', fg='black')
        self.search_entry.pack(side=tk.LEFT, padx=(5, 5), fill=tk.X, expand=True)

        # 绑定事件
        self.search_entry.bind('<Return>', lambda event: self.smart_search())

        # 搜索提示
        tip_frame = ttk.Frame(search_frame)
        tip_frame.pack(fill=tk.X, pady=(0, 5))

        self.search_tip = ttk.Label(tip_frame, text="💡 属性模式: 支持 text=登录、resource-id=com.example:id/button 或直接粘贴元素字典内容", foreground="gray", font=('Microsoft YaHei', 8))
        self.search_tip.pack(side=tk.LEFT)

        # 搜索按钮
        search_btn_frame = ttk.Frame(search_frame)
        search_btn_frame.pack(fill=tk.X)

        self.search_btn = ttk.Button(search_btn_frame, text="🔍 搜索并标记", command=self.smart_search)
        self.search_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.clear_search_btn = ttk.Button(search_btn_frame, text="🧹 清除标记", command=self.clear_search_marks)
        self.clear_search_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 功能按钮
        button_frame = ttk.Frame(right_frame)
        button_frame.pack(fill=tk.X)

        # 所有按钮放在一行
        button_row = ttk.Frame(button_frame)
        button_row.pack(fill=tk.X, pady=(0, 5))

        # 复制文字按钮
        self.copy_text_btn = ttk.Button(button_row, text="📋 复制文字", command=self.copy_element_text)
        self.copy_text_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 复制指纹按钮
        self.copy_fingerprint_btn = ttk.Button(button_row, text="🔍 复制指纹", command=self.copy_element_fingerprint)
        self.copy_fingerprint_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 测试指纹匹配按钮
        self.test_fingerprint_btn = ttk.Button(button_row, text="🧪 测试指纹", command=self.test_fingerprint_match)
        self.test_fingerprint_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 测试坐标按钮
        self.test_coord_btn = ttk.Button(button_row, text="🎯 测试坐标", command=self.test_coordinates)
        self.test_coord_btn.pack(side=tk.LEFT, padx=(0, 3))

        # 点击坐标按钮
        self.click_btn = ttk.Button(button_row, text="👆 点击坐标", command=self.click_coordinates)
        self.click_btn.pack(side=tk.LEFT)

    def display_screenshot(self, photo):
        """显示截图"""
        for widget in self.screenshot_frame.winfo_children():
            widget.destroy()

        self.screenshot_label = ttk.Label(self.screenshot_frame, image=photo, cursor="crosshair")
        self.screenshot_label.image = photo
        self.screenshot_label.pack()

        self.screenshot_label.bind("<Button-1>", self.on_screenshot_click)

        self.screenshot_frame.update_idletasks()
        self.screenshot_canvas.configure(scrollregion=self.screenshot_canvas.bbox("all"))

        # 根据设置决定是否显示边框
        if (self.show_bounds.get() and self.ui_hierarchy is not None) or (self.show_ocr.get() and self.ocr_results):
            self.show_all_elements_overlay()

    def on_screenshot_click(self, event):
        """处理截图点击事件"""
        if not self.current_screenshot:
            messagebox.showwarning("警告", "请先刷新截图")
            return

        # 计算实际坐标
        actual_x = int(event.x / self.scale_factor)
        actual_y = int(event.y / self.scale_factor)

        self.coord_label.config(text=f"({actual_x}, {actual_y})")

        # 获取当前选中的标签页
        current_tab = self.info_notebook.index(self.info_notebook.select())

        if current_tab == 0:  # UI元素信息标签页
            if self.ui_hierarchy is None:
                messagebox.showwarning("警告", "UI层次结构未获取，无法进行元素识别")
                return

            # 清除之前的标记和指纹信息
            self.marked_coordinates.clear()

            # 查找对应的UI元素
            element = self.find_element_at_position(actual_x, actual_y)

            if element is not None:
                self.current_element = element
                self.display_element_info(element, actual_x, actual_y)
            else:
                self.current_element = None
                self.info_text.delete(1.0, tk.END)
                self.info_text.insert(tk.END, f"🎯 点击坐标: ({actual_x}, {actual_y})\n\n❌ 未找到对应的UI元素")

        elif current_tab == 1:  # OCR文字信息标签页
            if not self.ocr_results:
                messagebox.showwarning("警告", "OCR结果未获取，请先进行OCR识别")
                return

            # 查找点击位置的OCR文字
            self.find_ocr_at_position(actual_x, actual_y)

    def find_element_at_position(self, x, y):
        """查找指定位置的UI元素（使用Python脚本的逻辑）"""
        if self.ui_hierarchy is None:
            return None

        candidates = []

        # 遍历所有元素
        for element in self.ui_hierarchy.findall('.//*'):
            bounds = element.get('bounds')
            if not bounds:
                continue

            # 解析bounds
            bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
            if not bounds_match:
                continue

            x1, y1, x2, y2 = map(int, bounds_match.groups())

            # 检查点击位置是否在元素范围内
            if x1 <= x <= x2 and y1 <= y <= y2:
                area = (x2 - x1) * (y2 - y1)
                candidates.append({
                    'element': element,
                    'area': area,
                    'bounds': (x1, y1, x2, y2)
                })

        if not candidates:
            return None

        # 选择面积最小的元素（最精确匹配）- 完全按照Python脚本的逻辑
        best_candidate = min(candidates, key=lambda x: x['area'])
        return best_candidate['element']

    def find_ocr_at_position(self, x, y):
        """查找指定位置的OCR文字"""
        if not self.ocr_results:
            return

        # 清除之前的标记
        self.marked_coordinates.clear()

        # 查找点击位置的OCR文字
        found_ocr = None
        for ocr_item in self.ocr_results:
            bbox = ocr_item.get('bbox', [])
            if len(bbox) >= 4:
                x1, y1, x2, y2 = bbox
                if x1 <= x <= x2 and y1 <= y <= y2:
                    found_ocr = ocr_item
                    break

        # 显示OCR信息
        self.ocr_text_display.delete(1.0, tk.END)
        if found_ocr:
            self.ocr_text_display.insert(tk.END, f"🎯 点击坐标: ({x}, {y})\n")
            self.ocr_text_display.insert(tk.END, f"✅ 找到OCR文字\n\n")
            self.ocr_text_display.insert(tk.END, f"📝 识别文字: {found_ocr.get('text', '')}\n")
            self.ocr_text_display.insert(tk.END, f"🎯 置信度: {found_ocr.get('confidence', 0):.2f}\n")

            bbox = found_ocr.get('bbox', [])
            if len(bbox) >= 4:
                x1, y1, x2, y2 = bbox
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2
                width = x2 - x1
                height = y2 - y1

                self.ocr_text_display.insert(tk.END, f"\n📐 位置信息:\n")
                self.ocr_text_display.insert(tk.END, f"左上角: ({x1}, {y1})\n")
                self.ocr_text_display.insert(tk.END, f"右下角: ({x2}, {y2})\n")
                self.ocr_text_display.insert(tk.END, f"中心点: ({center_x}, {center_y})\n")
                self.ocr_text_display.insert(tk.END, f"尺寸: {width} × {height}\n")

                # 标记中心点
                self.marked_coordinates.append((center_x, center_y))
        else:
            self.ocr_text_display.insert(tk.END, f"🎯 点击坐标: ({x}, {y})\n\n❌ 未找到对应的OCR文字")

        # 更新显示
        if self.current_screenshot:
            self._update_screenshot_display()

    def display_element_info(self, element, click_x, click_y):
        """显示元素信息（合并显示所有信息）"""
        # 清除之前的信息
        self.info_text.delete(1.0, tk.END)

        # 基本信息
        self.info_text.insert(tk.END, f"🎯 点击坐标: ({click_x}, {click_y})\n")
        self.info_text.insert(tk.END, f"✅ 找到UI元素\n\n")

        # 元素属性
        self.info_text.insert(tk.END, "📋 元素属性:\n")
        self.info_text.insert(tk.END, "-" * 40 + "\n")

        # 显示所有属性
        for attr_name, attr_value in element.attrib.items():
            if attr_value:  # 只显示非空属性
                self.info_text.insert(tk.END, f"{attr_name}: {attr_value}\n")

        # 元素文本内容
        if element.text and element.text.strip():
            self.info_text.insert(tk.END, f"\n📝 文本内容: {element.text.strip()}\n")

        # 解析bounds获取坐标信息
        bounds = element.get('bounds')
        if bounds:
            bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
            if bounds_match:
                x1, y1, x2, y2 = map(int, bounds_match.groups())
                width = x2 - x1
                height = y2 - y1
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2

                self.info_text.insert(tk.END, f"\n📐 位置信息:\n")
                self.info_text.insert(tk.END, f"左上角: ({x1}, {y1})\n")
                self.info_text.insert(tk.END, f"右下角: ({x2}, {y2})\n")
                self.info_text.insert(tk.END, f"中心点: ({center_x}, {center_y})\n")
                self.info_text.insert(tk.END, f"尺寸: {width} × {height}\n")

        # 添加指纹信息到同一面板
        fingerprint = self.extract_element_fingerprint(element, click_x, click_y)

        fp_info = f"🎯 点击坐标: ({click_x}, {click_y})\n"
        fp_info += "=" * 50 + "\n"
        fp_info += "🔍 元素指纹特征分析\n"
        fp_info += "=" * 50 + "\n\n"

        # 指纹哈希
        fp_info += f"🆔 指纹哈希: {fingerprint['fingerprint_hash']}\n"
        fp_info += f"⭐ 唯一性评分: {fingerprint['uniqueness_score']}/100\n\n"

        # 基础属性特征
        fp_info += "📝 基础属性特征:\n"
        fp_info += "-" * 30 + "\n"
        basic = fingerprint['basic_attributes']
        for key, value in basic.items():
            if value:
                fp_info += f"  {key}: {value}\n"

        # 位置特征
        fp_info += f"\n📍 位置特征:\n"
        fp_info += "-" * 30 + "\n"
        position = fingerprint['position_features']
        for key, value in position.items():
            fp_info += f"  {key}: {value}\n"

        # 状态特征
        fp_info += f"\n⚙️ 状态特征:\n"
        fp_info += "-" * 30 + "\n"
        states = fingerprint['state_features']
        for key, value in states.items():
            fp_info += f"  {key}: {'是' if value else '否'}\n"

        # 上下文特征
        fp_info += f"\n� 上下文特征:\n"
        fp_info += "-" * 30 + "\n"
        context = fingerprint['context_features']
        for key, value in context.items():
            if value:
                fp_info += f"  {key}: {value}\n"

        # 匹配建议
        fp_info += f"\n💡 匹配建议:\n"
        fp_info += "-" * 30 + "\n"
        suggestions = self._generate_matching_suggestions(fingerprint)
        for suggestion in suggestions:
            fp_info += f"  • {suggestion}\n"

        # 显示完整指纹信息
        self.info_text.insert(tk.END, fp_info)

    def test_coordinate(self):
        """测试坐标功能 - 在截图上标记当前选中元素的中心点"""
        if not self.current_element:
            messagebox.showwarning("警告", "请先点击选择一个UI元素")
            return

        bounds = self.current_element.get('bounds')
        if not bounds:
            messagebox.showwarning("警告", "当前元素没有坐标信息")
            return

        bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
        if not bounds_match:
            messagebox.showwarning("警告", "坐标格式解析失败")
            return

        x1, y1, x2, y2 = map(int, bounds_match.groups())
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        # 添加到标记坐标列表
        self.marked_coordinates.append((center_x, center_y))

        # 重新显示截图并标记坐标
        if self.current_screenshot:
            self._update_screenshot_display()

        messagebox.showinfo("测试坐标", f"已在截图上标记坐标: ({center_x}, {center_y})")

    def toggle_bounds_display(self):
        """切换UI边框显示（互斥模式）"""
        if self.show_bounds.get():
            # 开启UI边框时，关闭OCR边框
            self.show_ocr.set(False)

        if self.current_screenshot:
            self._update_screenshot_display()

    def toggle_ocr_display(self):
        """切换OCR边框显示（互斥模式）"""
        if self.show_ocr.get():
            # 开启OCR边框时，关闭UI边框
            self.show_bounds.set(False)

            if not self.ocr_results:
                # 首次开启OCR，获取OCR结果
                self._fetch_ocr_results()
                return

        if self.current_screenshot:
            self._update_screenshot_display()

    def _fetch_ocr_results(self):
        """获取OCR结果"""
        def fetch():
            try:
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(text="📝 正在进行OCR识别...", foreground="blue"))

                # 调用后端OCR API（和index页面一样传递method参数）
                response = requests.post(f"{self.api_base}/ocr", json={"method": "paddleocr"}, timeout=30)
                if response.status_code != 200:
                    raise Exception(f"OCR请求失败: {response.status_code}")

                result = response.json()
                print(f"OCR API返回结果: {result}")  # 调试信息

                if not result.get('success'):
                    raise Exception(result.get('error', 'OCR识别失败'))

                # 解析OCR结果（和index页面一样使用text字段）
                ocr_data = result.get('text', []) or result.get('results', [])
                print(f"解析到的OCR数据: {ocr_data}")  # 调试信息
                self.ocr_results = []

                # 转换OCR结果格式
                for i, item in enumerate(ocr_data):
                    print(f"OCR项目 {i}: {item}")  # 调试每个项目

                    if isinstance(item, dict):
                        # 后端返回的字典格式: {'bounds': {'x': 828, 'y': 648, 'width': 186, 'height': 23}, 'confidence': 0.972, 'text': '短剧百宝箱'}
                        bounds = item.get('bounds', {})
                        text = item.get('text', '')
                        confidence = item.get('confidence', 0.0)

                        if bounds and text:
                            x = bounds.get('x', 0)
                            y = bounds.get('y', 0)
                            width = bounds.get('width', 0)
                            height = bounds.get('height', 0)

                            ocr_item = {
                                'text': text,
                                'confidence': confidence,
                                'bbox': [int(x), int(y), int(x + width), int(y + height)]
                            }
                            print(f"  添加OCR结果: {ocr_item}")
                            self.ocr_results.append(ocr_item)
                        else:
                            print(f"  跳过项目：bounds或text为空")
                    elif isinstance(item, list) and len(item) >= 2:
                        # PaddleOCR格式: [[[x1,y1],[x2,y2],[x3,y3],[x4,y4]], (text, confidence)]
                        bbox_points = item[0]
                        text_info = item[1]
                        print(f"  bbox_points: {bbox_points}")
                        print(f"  text_info: {text_info}")

                        if len(bbox_points) >= 4 and len(text_info) >= 2:
                            # 计算边界框
                            x_coords = [p[0] for p in bbox_points]
                            y_coords = [p[1] for p in bbox_points]
                            x1, x2 = min(x_coords), max(x_coords)
                            y1, y2 = min(y_coords), max(y_coords)

                            ocr_item = {
                                'text': text_info[0],
                                'confidence': text_info[1],
                                'bbox': [int(x1), int(y1), int(x2), int(y2)]
                            }
                            print(f"  添加OCR结果: {ocr_item}")
                            self.ocr_results.append(ocr_item)
                        else:
                            print(f"  跳过项目：bbox_points长度={len(bbox_points)}, text_info长度={len(text_info) if hasattr(text_info, '__len__') else 'N/A'}")
                    else:
                        print(f"  跳过项目：不是列表或长度不足，类型={type(item)}, 长度={len(item) if hasattr(item, '__len__') else 'N/A'}")

                print(f"OCR识别成功，共识别到 {len(self.ocr_results)} 个文本区域")

                # 更新OCR文字显示
                self.root.after(0, self._update_ocr_text_display)

                # 更新显示
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(text="✅ OCR识别完成", foreground="green"))
                self.root.after(0, self._update_screenshot_display)

            except Exception as e:
                print(f"OCR识别失败: {str(e)}")
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.root.after(0, lambda: self.status_label.config(text="❌ OCR识别失败", foreground="red"))
                self.root.after(0, lambda: messagebox.showerror("错误", f"OCR识别失败: {str(e)}"))
                # 关闭OCR开关
                self.root.after(0, lambda: self.show_ocr.set(False))

        threading.Thread(target=fetch, daemon=True).start()

    def _update_ocr_text_display(self):
        """更新OCR文字显示"""
        if not self.ocr_text_display:
            return

        self.ocr_text_display.delete(1.0, tk.END)

        if not self.ocr_results:
            self.ocr_text_display.insert(tk.END, "📝 暂无OCR识别结果\n\n请点击 '📝 OCR边框' 按钮进行文字识别")
            return

        self.ocr_text_display.insert(tk.END, f"📝 OCR识别结果 (共{len(self.ocr_results)}个文本区域)\n")
        self.ocr_text_display.insert(tk.END, "=" * 50 + "\n\n")

        for i, item in enumerate(self.ocr_results, 1):
            text = item.get('text', '')
            confidence = item.get('confidence', 0)
            bbox = item.get('bbox', [])

            self.ocr_text_display.insert(tk.END, f"🔸 文本区域 {i}:\n")
            self.ocr_text_display.insert(tk.END, f"   📄 内容: {text}\n")
            self.ocr_text_display.insert(tk.END, f"   🎯 置信度: {confidence:.2f}\n")
            if len(bbox) == 4:
                self.ocr_text_display.insert(tk.END, f"   📍 位置: ({bbox[0]},{bbox[1]}) - ({bbox[2]},{bbox[3]})\n")
            self.ocr_text_display.insert(tk.END, "\n")

        # 滚动到顶部
        self.ocr_text_display.see(1.0)

    def _update_screenshot_display(self):
        """更新截图显示（包含UI边框和OCR边框）"""
        if not self.current_screenshot:
            return

        # 显示边框（内部已做互斥判断）
        if (self.show_bounds.get() and self.ui_hierarchy is not None) or (self.show_ocr.get() and self.ocr_results):
            self.show_all_elements_overlay()
        else:
            # 重新显示原始截图
            img_width, img_height = self.current_screenshot.size
            new_width = int(img_width * self.scale_factor)
            new_height = int(img_height * self.scale_factor)

            resized_image = self.current_screenshot.resize((new_width, new_height), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(resized_image)

            # 直接更新显示，不触发递归
            if self.screenshot_label is not None:
                self.screenshot_label.configure(image=photo)
                self.screenshot_label.image = photo

    def show_all_elements_overlay(self):
        """显示所有UI元素的边框覆盖层（参考Python脚本的绘制逻辑）"""
        if not self.current_screenshot:
            return

        # 创建图像副本
        img = self.current_screenshot.copy().convert('RGBA')
        overlay = Image.new('RGBA', img.size, (255, 255, 255, 0))
        draw = ImageDraw.Draw(overlay)

        # 绘制UI元素边框（只在OCR关闭时显示）
        if self.show_bounds.get() and self.ui_hierarchy is not None and not self.show_ocr.get():
            for element in self.ui_hierarchy.findall('.//*'):
                bounds = element.get('bounds')
                if not bounds:
                    continue

                bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if not bounds_match:
                    continue

                x1, y1, x2, y2 = map(int, bounds_match.groups())

                # 过滤掉太小的元素
                if (x2 - x1) < 10 or (y2 - y1) < 10:
                    continue

                # 根据元素类型选择颜色（参考Python脚本）
                clickable = element.get('clickable', 'false').lower() == 'true'
                class_name = element.get('class', '')
                text = element.get('text', '')

                if 'Button' in class_name:
                    color = 'green'
                elif clickable:
                    color = 'red'
                elif text:
                    color = 'cyan'
                else:
                    color = 'blue'

                # 绘制边框（参考Python脚本的绘制方式）
                draw.rectangle([x1, y1, x2, y2], outline=color, width=2)

                # 可点击元素额外红色边框
                if clickable:
                    draw.rectangle([x1-1, y1-1, x2+1, y2+1], outline='red', width=1)

                # 如果是搜索匹配的元素，绘制特殊标记
                if hasattr(self, 'search_matches') and element in self.search_matches:
                    # 绘制黄色高亮边框
                    draw.rectangle([x1-3, y1-3, x2+3, y2+3], outline='yellow', width=4)

                    # 在左上角绘制搜索标记
                    try:
                        font = ImageFont.truetype("arial.ttf", 16)
                    except:
                        font = ImageFont.load_default()

                    mark_text = "🔍"
                    draw.text((x1, y1-20), mark_text, fill='yellow', font=font)

        # 绘制OCR边框（只在UI边框关闭时显示）
        if self.show_ocr.get() and self.ocr_results and not self.show_bounds.get():
            for ocr_item in self.ocr_results:
                # OCR结果格式：{"text": "文字", "bbox": [x1, y1, x2, y2], "confidence": 0.95}
                bbox = ocr_item.get('bbox', [])
                if len(bbox) == 4:
                    x1, y1, x2, y2 = bbox
                    # 绘制橙色OCR边框
                    draw.rectangle([x1, y1, x2, y2], outline='orange', width=2)

        # 绘制标记的坐标点（地图标记样式，和原Python脚本一致）
        for mark_x, mark_y in self.marked_coordinates:
            self._draw_map_marker(draw, mark_x, mark_y, "★", color=(255, 215, 0, 220), size=40)

            # 绘制坐标文本（带背景）
            text = f"({mark_x},{mark_y})"
            try:
                font = ImageFont.truetype("arial.ttf", 14)
            except:
                font = ImageFont.load_default()

            # 计算文字尺寸
            text_bbox = draw.textbbox((0, 0), text, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]

            # 绘制文字背景
            text_x = mark_x + 25
            text_y = mark_y - 25
            draw.rectangle([text_x-3, text_y-3, text_x+text_width+3, text_y+text_height+3],
                          fill='white', outline='blue', width=2)
            draw.text((text_x, text_y), text, fill='blue', font=font)

        # 合并图层
        combined = Image.alpha_composite(img, overlay)
        combined = combined.convert('RGB')

        # 调整图像大小并显示
        img_width, img_height = combined.size
        new_width = int(img_width * self.scale_factor)
        new_height = int(img_height * self.scale_factor)

        display_img = combined.resize((new_width, new_height), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(display_img)

        # 更新显示
        if self.screenshot_label is not None:
            self.screenshot_label.configure(image=photo)
            self.screenshot_label.image = photo

    def _draw_map_marker(self, draw, x, y, label, color=(255, 0, 0), size=30):
        """绘制简洁清晰的标记点（和原Python脚本一致）"""
        # 使用简洁的圆形标记
        radius = size // 2

        # 绘制外圈（白色边框）
        draw.ellipse([
            x - radius - 2, y - radius - 2,
            x + radius + 2, y + radius + 2
        ], fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=1)

        # 绘制主体圆形
        draw.ellipse([
            x - radius, y - radius,
            x + radius, y + radius
        ], fill=color, outline=(255, 255, 255, 255), width=2)

        # 绘制标签
        if label:
            try:
                font = ImageFont.truetype("arial.ttf", min(16, size // 2))
            except:
                font = ImageFont.load_default()

            # 计算文字位置（居中）
            label_bbox = draw.textbbox((0, 0), str(label), font=font)
            label_width = label_bbox[2] - label_bbox[0]
            label_height = label_bbox[3] - label_bbox[1]

            text_x = x - label_width // 2
            text_y = y - label_height // 2

            # 绘制白色文字
            draw.text((text_x, text_y), str(label), fill=(255, 255, 255, 255), font=font)

    def on_canvas_configure(self, event):
        """画布配置事件"""
        self.screenshot_canvas.configure(scrollregion=self.screenshot_canvas.bbox("all"))

    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        self.screenshot_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def smart_search(self):
        """智能搜索功能"""
        search_content = self.search_entry.get().strip()
        if not search_content:
            messagebox.showwarning("警告", "请输入搜索内容")
            return

        if self.ui_hierarchy is None:
            messagebox.showwarning("警告", "请先获取UI结构")
            return

        # 清除之前的搜索结果
        self.search_matches = []

        # 解析搜索内容
        search_criteria = self._parse_search_content(search_content)

        # 搜索匹配的元素
        for element in self.ui_hierarchy.findall('.//*'):
            if self._element_matches_criteria(element, search_criteria):
                self.search_matches.append(element)

        if self.search_matches:
            messagebox.showinfo("搜索结果", f"找到 {len(self.search_matches)} 个匹配元素")
            # 重新显示截图并标记搜索结果
            self._update_screenshot_display()
        else:
            messagebox.showinfo("搜索结果", "未找到匹配的元素")

    def _parse_search_content(self, content):
        """解析搜索内容"""
        criteria = {}

        # 尝试解析属性=值的格式
        if '=' in content:
            for pair in content.split(','):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    criteria[key.strip()] = value.strip()
        else:
            # 简单文本搜索
            criteria['text'] = content

        return criteria

    def _element_matches_criteria(self, element, criteria):
        """检查元素是否匹配搜索条件"""
        for key, value in criteria.items():
            element_value = element.get(key, '')
            if value.lower() not in element_value.lower():
                return False
        return True

    def clear_search_marks(self):
        """清除搜索标记"""
        if hasattr(self, 'search_matches'):
            self.search_matches = []

        # 恢复原始截图显示
        if self.current_screenshot:
            self._update_screenshot_display()

    def copy_element_text(self):
        """复制当前元素的文字内容"""
        if self.current_element is None:
            messagebox.showwarning("警告", "请先点击屏幕上的元素")
            return

        # 获取元素的文字内容
        text = self.current_element.get('text', '').strip()
        content_desc = self.current_element.get('content-desc', '').strip()

        # 优先复制text，如果没有则复制content-desc
        copy_text = text if text else content_desc

        if not copy_text:
            messagebox.showwarning("警告", "该元素没有文字内容")
            return

        # 复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(copy_text)

        messagebox.showinfo("成功", f"已复制文字: {copy_text[:50]}{'...' if len(copy_text) > 50 else ''}")

    def copy_element_fingerprint(self):
        """复制当前元素的指纹特征（紧凑型）"""
        if self.current_element is None:
            messagebox.showwarning("警告", "请先点击屏幕上的元素")
            return

        # 生成简化的指纹特征
        fingerprint = self._extract_compact_fingerprint(self.current_element)

        # 转换为紧凑JSON格式（单行）
        import json
        fingerprint_string = json.dumps(fingerprint, ensure_ascii=False, separators=(',', ':'))

        # 复制到剪贴板
        self.root.clipboard_clear()
        self.root.clipboard_append(fingerprint_string)

        messagebox.showinfo("成功", "元素指纹特征已复制到剪贴板（紧凑格式）")


    def test_coordinates(self):
        """测试元素坐标并在图中标记"""
        if self.current_element is None:
            messagebox.showwarning("警告", "请先点击屏幕上的元素")
            return

        bounds = self.current_element.get('bounds')
        if not bounds:
            messagebox.showwarning("警告", "当前元素没有坐标信息")
            return

        bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
        if not bounds_match:
            messagebox.showwarning("警告", "坐标格式解析失败")
            return

        x1, y1, x2, y2 = map(int, bounds_match.groups())
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        # 清除之前的标记，只保留最新的
        self.marked_coordinates.clear()
        self.marked_coordinates.append((center_x, center_y))

        # 显示测试模式：只显示标点和当前控件
        self._show_test_mode()

        messagebox.showinfo("测试坐标", f"已在截图上标记坐标: ({center_x}, {center_y})")

    def _show_test_mode(self):
        """测试模式：只显示标点和当前控件"""
        if not self.current_screenshot:
            return

        # 创建图像副本
        img = self.current_screenshot.copy().convert('RGBA')
        overlay = Image.new('RGBA', img.size, (255, 255, 255, 0))
        draw = ImageDraw.Draw(overlay)

        # 只绘制当前选中的控件边框
        if self.current_element is not None:
            bounds = self.current_element.get('bounds')
            if bounds:
                bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
                if bounds_match:
                    x1, y1, x2, y2 = map(int, bounds_match.groups())
                    # 绘制绿色边框表示当前选中的控件
                    draw.rectangle([x1, y1, x2, y2], outline='lime', width=4)

        # 绘制标记的坐标点（使用和原Python脚本一样的地图标记样式）
        for mark_x, mark_y in self.marked_coordinates:
            # 使用地图标记样式，size=45和原Python脚本一致
            self._draw_map_marker(draw, mark_x, mark_y, "UI", color=(0, 200, 0, 220), size=45)

        # 合并图像
        combined = Image.alpha_composite(img, overlay)

        # 缩放并显示
        img_width, img_height = combined.size
        new_width = int(img_width * self.scale_factor)
        new_height = int(img_height * self.scale_factor)

        resized_image = combined.resize((new_width, new_height), Image.Resampling.LANCZOS)
        photo = ImageTk.PhotoImage(resized_image)

        # 直接更新显示，不触发递归
        if self.screenshot_label is not None:
            self.screenshot_label.configure(image=photo)
            self.screenshot_label.image = photo

    def click_coordinates(self):
        """执行点击操作"""
        if self.current_element is None:
            messagebox.showwarning("警告", "请先点击屏幕上的元素")
            return

        bounds = self.current_element.get('bounds')
        if not bounds:
            messagebox.showwarning("警告", "当前元素没有坐标信息")
            return

        bounds_match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
        if not bounds_match:
            messagebox.showwarning("警告", "坐标格式解析失败")
            return

        x1, y1, x2, y2 = map(int, bounds_match.groups())
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2

        try:
            if not self.is_backend_connected:
                messagebox.showerror("错误", "后端未连接")
                return

            # 调用后端点击API
            response = requests.post(f"{self.api_base}/click",
                                   json={'x': center_x, 'y': center_y}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    messagebox.showinfo("成功", f"已点击坐标 ({center_x}, {center_y})")
                    # 自动刷新截图
                    self.root.after(1000, self.capture_screenshot)
                else:
                    messagebox.showerror("错误", f"点击失败: {result.get('error', '未知错误')}")
            else:
                messagebox.showerror("错误", f"点击请求失败: {response.status_code}")

        except Exception as e:
            messagebox.showerror("错误", f"点击操作失败: {str(e)}")

    def extract_element_fingerprint(self, element, click_x=None, click_y=None):
        """提取元素指纹特征（完全按照原Python脚本逻辑）"""
        import hashlib

        # 基础属性特征
        text = element.get('text', '').strip()
        class_name = element.get('class', '')
        resource_id = element.get('resource-id', '')
        content_desc = element.get('content-desc', '')

        # 位置特征
        bounds = element.get('bounds', '')
        position_info = self._extract_position_features(bounds)

        # 状态特征
        state_features = {
            'clickable': element.get('clickable', 'false') == 'true',
            'enabled': element.get('enabled', 'true') == 'true',
            'focusable': element.get('focusable', 'false') == 'true',
            'checkable': element.get('checkable', 'false') == 'true',
            'scrollable': element.get('scrollable', 'false') == 'true',
            'long_clickable': element.get('long-clickable', 'false') == 'true',
            'password': element.get('password', 'false') == 'true'
        }

        # 生成指纹特征
        fingerprint = {
            'basic_attributes': {
                'text': text,
                'class': class_name,
                'resource_id': resource_id,
                'content_desc': content_desc,
                'package': element.get('package', ''),
                'index': element.get('index', '0')
            },
            'position_features': position_info,
            'state_features': state_features,
            'context_features': self._extract_context_features(element),
            'fingerprint_hash': self._generate_fingerprint_hash(element),
            'uniqueness_score': self._calculate_uniqueness_score(element)
        }

        return fingerprint

    def _extract_position_features(self, bounds):
        """提取位置特征"""
        if not bounds:
            return {}

        match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
        if not match:
            return {}

        x1, y1, x2, y2 = map(int, match.groups())
        center_x = (x1 + x2) // 2
        center_y = (y1 + y2) // 2
        width = x2 - x1
        height = y2 - y1

        # 获取屏幕尺寸（模拟，因为没有设备连接）
        screen_width = 1080  # 默认值
        screen_height = 1920  # 默认值

        return {
            'bounds': bounds,
            'center_x': center_x,
            'center_y': center_y,
            'width': width,
            'height': height,
            'relative_x': round(center_x / screen_width, 3),
            'relative_y': round(center_y / screen_height, 3),
            'relative_width': round(width / screen_width, 3),
            'relative_height': round(height / screen_height, 3)
        }

    def _extract_context_features(self, element):
        """提取上下文特征"""
        context = {}

        # 父元素信息
        parent = self._find_parent_element(element)
        if parent is not None:
            context['parent_class'] = parent.get('class', '')
            context['parent_resource_id'] = parent.get('resource-id', '')

        # 兄弟元素数量
        if parent is not None:
            siblings = list(parent)
            context['sibling_count'] = len(siblings)
            context['element_index'] = siblings.index(element) if element in siblings else -1

        return context

    def _find_parent_element(self, target_element):
        """查找父元素"""
        if self.ui_hierarchy is None:
            return None

        def find_parent_recursive(element, target):
            for child in element:
                if child == target:
                    return element
                result = find_parent_recursive(child, target)
                if result is not None:
                    return result
            return None

        return find_parent_recursive(self.ui_hierarchy, target_element)

    def _generate_fingerprint_hash(self, element):
        """生成元素指纹哈希"""
        import hashlib

        # 获取关键属性
        key_attrs = [
            element.get('resource-id', ''),
            element.get('text', ''),
            element.get('content-desc', ''),
            element.get('class', ''),
            element.get('bounds', ''),
            element.get('index', '0')
        ]

        # 生成哈希
        combined = '|'.join(key_attrs)
        return hashlib.md5(combined.encode('utf-8')).hexdigest()[:12]

    def _calculate_uniqueness_score(self, element):
        """计算唯一性评分"""
        score = 0

        # 资源ID权重最高
        if element.get('resource-id', ''):
            score += 40

        # 文本内容
        if element.get('text', '').strip():
            score += 25

        # 内容描述
        if element.get('content-desc', ''):
            score += 20

        # 类名
        if element.get('class', ''):
            score += 10

        # 位置信息
        if element.get('bounds', ''):
            score += 5

        return min(score, 100)

    def _generate_matching_suggestions(self, fingerprint):
        """生成匹配建议"""
        suggestions = []
        basic = fingerprint['basic_attributes']

        # 基于唯一性评分给出建议
        score = fingerprint['uniqueness_score']
        if score >= 80:
            suggestions.append("元素特征明显，推荐使用resource-id或text进行匹配")
        elif score >= 60:
            suggestions.append("元素特征较好，可使用多个属性组合匹配")
        else:
            suggestions.append("元素特征不明显，建议结合位置信息进行匹配")

        # 具体建议
        if basic['resource_id']:
            suggestions.append(f"推荐使用resource-id: {basic['resource_id']}")
        if basic['text']:
            suggestions.append(f"推荐使用text: {basic['text']}")
        if basic['content_desc']:
            suggestions.append(f"推荐使用content-desc: {basic['content_desc']}")

        return suggestions

    def _extract_compact_fingerprint(self, element, click_x=None, click_y=None):
        """提取紧凑型指纹特征（用于复制功能）"""
        import hashlib

        # 基础属性（只保留关键信息）
        text = element.get('text', '').strip()
        class_name = element.get('class', '')
        resource_id = element.get('resource-id', '')
        content_desc = element.get('content-desc', '')

        # 位置信息（简化）
        bounds = element.get('bounds', '')
        rel_pos = None
        if bounds:
            match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', bounds)
            if match:
                x1, y1, x2, y2 = map(int, match.groups())
                center_x = (x1 + x2) // 2
                center_y = (y1 + y2) // 2

                # 获取屏幕尺寸
                screen_width = 1080
                screen_height = 1920

                # 相对位置（保留3位小数）
                rel_pos = {
                    'x': round(center_x / screen_width, 3),
                    'y': round(center_y / screen_height, 3),
                    'w': round((x2 - x1) / screen_width, 3),
                    'h': round((y2 - y1) / screen_height, 3)
                }

        # 上下文信息（简化）
        parent = self._find_parent_element(element)
        parent_id = parent.get('resource-id', '') if parent is not None else ''
        parent_class = parent.get('class', '') if parent is not None else ''

        # 状态信息（只保留关键状态）
        key_states = {}
        important_states = ['clickable', 'enabled', 'checkable', 'scrollable']
        for state in important_states:
            if element.get(state, 'false') == 'true':
                key_states[state] = True

        # 生成紧凑指纹哈希
        hash_components = [
            resource_id,
            text,
            content_desc,
            class_name,
            f"{rel_pos['x']},{rel_pos['y']}" if rel_pos else '',
            element.get('index', '0')
        ]

        combined = '|'.join(filter(None, hash_components))
        fingerprint_hash = hashlib.md5(combined.encode('utf-8')).hexdigest()[:8]

        # 计算唯一性评分
        uniqueness = self._calculate_compact_uniqueness(element)

        # 构建紧凑指纹特征
        compact_fingerprint = {
            'hash': fingerprint_hash,
            'score': uniqueness
        }

        # 只添加非空的关键属性
        if resource_id:
            compact_fingerprint['id'] = resource_id
        if text:
            compact_fingerprint['text'] = text
        if content_desc:
            compact_fingerprint['desc'] = content_desc
        if class_name:
            compact_fingerprint['class'] = class_name
        if rel_pos:
            compact_fingerprint['pos'] = rel_pos
        if parent_id:
            compact_fingerprint['parent_id'] = parent_id
        elif parent_class:
            compact_fingerprint['parent_class'] = parent_class
        if key_states:
            compact_fingerprint['states'] = key_states

        return compact_fingerprint

    def _calculate_compact_uniqueness(self, element):
        """计算紧凑型唯一性评分"""
        score = 0

        # 资源ID权重最高
        if element.get('resource-id', ''):
            score += 40

        # 文本内容
        if element.get('text', '').strip():
            score += 25

        # 内容描述
        if element.get('content-desc', ''):
            score += 20

        # 类名
        if element.get('class', ''):
            score += 10

        # 位置信息
        if element.get('bounds', ''):
            score += 5

        return min(score, 100)

    def test_fingerprint_match(self):
        """测试指纹匹配功能"""
        if self.current_element is None:
            messagebox.showwarning("警告", "请先点击屏幕上的元素")
            return

        try:
            import json
            # 生成当前元素的指纹
            fingerprint = self._extract_compact_fingerprint(self.current_element)
            fingerprint_json = json.dumps(fingerprint, ensure_ascii=False, separators=(',', ':'))

            # 调用后端匹配API
            response = requests.post(f"{self.api_base}/ui/match-fingerprint",
                                   json={"fingerprint": fingerprint_json},
                                   timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    match_result = result.get('result')
                    if match_result and match_result.get('found'):
                        element_info = match_result.get('element')
                        confidence = match_result.get('confidence', 0)
                        match_reason = match_result.get('match_reason', '')

                        # 显示匹配结果
                        message = f"✅ 指纹匹配成功！\n\n"
                        message += f"匹配置信度: {confidence:.2%}\n"
                        message += f"匹配原因: {match_reason}\n\n"
                        message += f"匹配元素信息:\n"
                        message += f"• 类名: {element_info.get('class', 'N/A')}\n"
                        message += f"• 资源ID: {element_info.get('resource_id', 'N/A')}\n"
                        message += f"• 文本: {element_info.get('text', 'N/A')}\n"
                        message += f"• 位置: ({element_info.get('x', 0)}, {element_info.get('y', 0)})\n"
                        message += f"• 尺寸: {element_info.get('width', 0)}x{element_info.get('height', 0)}\n"

                        # 检查位置是否准确
                        original_bounds = self.current_element.get('bounds', '')
                        if original_bounds:
                            import re
                            match = re.match(r'\[(\d+),(\d+)\]\[(\d+),(\d+)\]', original_bounds)
                            if match:
                                orig_x1, orig_y1, orig_x2, orig_y2 = map(int, match.groups())
                                orig_center_x = (orig_x1 + orig_x2) // 2
                                orig_center_y = (orig_y1 + orig_y2) // 2

                                matched_center_x = element_info.get('x', 0) + element_info.get('width', 0) // 2
                                matched_center_y = element_info.get('y', 0) + element_info.get('height', 0) // 2

                                distance = ((orig_center_x - matched_center_x) ** 2 + (orig_center_y - matched_center_y) ** 2) ** 0.5
                                message += f"\n🎯 位置精度: 中心点偏差 {distance:.1f} 像素"

                                if distance <= 10:
                                    message += " (非常精确)"
                                elif distance <= 50:
                                    message += " (较精确)"
                                else:
                                    message += " (偏差较大)"

                        messagebox.showinfo("指纹匹配测试", message)
                    else:
                        messagebox.showwarning("指纹匹配测试", "❌ 指纹匹配失败：未找到匹配的元素")
                else:
                    error_msg = result.get('error', '未知错误')
                    messagebox.showerror("指纹匹配测试", f"❌ 指纹匹配失败：{error_msg}")
            else:
                messagebox.showerror("指纹匹配测试", f"❌ 请求失败：HTTP {response.status_code}")

        except Exception as e:
            messagebox.showerror("错误", f"测试指纹匹配失败: {str(e)}")


def main():
    """主函数"""
    root = tk.Tk()
    UIElementCaptureBackend(root)
    root.mainloop()


if __name__ == "__main__":
    main()
