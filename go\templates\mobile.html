<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🤖 移动端自动化控制台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
        }
        
        .container {
            max-width: 100%;
            padding: 10px;
            padding-bottom: 80px; /* 为底部导航留空间 */
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            height: calc(100vh - 60px); /* 减去底部导航高度 */
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            color: #2c3e50;
            padding: 20px 15px;
            border-radius: 15px;
            margin-bottom: 15px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 1.5em;
            margin-bottom: 5px;
            font-weight: 600;
        }
        
        .header p {
            font-size: 0.9em;
            opacity: 0.7;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            font-size: 1.2em;
            margin-bottom: 15px;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            width: 100%;
            margin: 8px 0;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn:active {
            transform: translateY(1px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.4);
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            width: auto;
            margin: 0;
            border-radius: 20px;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }
        
        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .status-row:last-child {
            border-bottom: none;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }
        
        .status-running {
            background: #2ecc71;
            box-shadow: 0 0 10px rgba(46, 204, 113, 0.5);
        }
        
        .status-stopped {
            background: #e74c3c;
            animation: none;
        }
        
        .status-pending {
            background: #f39c12;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        /* 应用分类样式 */
        .app-categories {
            display: flex;
            gap: 10px;
            padding: 10px 0;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .category-btn {
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 20px;
            color: white;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-btn.active {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }

        /* 应用工具栏 */
        .app-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            margin-bottom: 10px;
        }

        .toolbar-btn {
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .toolbar-btn.primary {
            background: #007AFF;
        }

        .task-item, .app-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 15px;
            margin-bottom: 12px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            user-select: none;
        }

        .task-item:active, .app-item:active {
            transform: scale(0.98);
        }

        .app-item.multi-select-mode {
            padding-left: 50px;
        }

        .app-item.selected {
            border-left-color: #007AFF;
            background: rgba(0, 122, 255, 0.1);
        }
        
        .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .item-title {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1em;
        }
        
        .item-subtitle {
            font-size: 0.85em;
            color: #7f8c8d;
            margin-top: 4px;
        }
        
        .item-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 10px;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
            z-index: 1000;
        }
        
        .nav-item {
            text-align: center;
            padding: 8px;
            border-radius: 10px;
            transition: all 0.3s ease;
            cursor: pointer;
            flex: 1;
        }
        
        .nav-item.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }
        
        .nav-item span {
            display: block;
            font-size: 1.2em;
            margin-bottom: 2px;
        }
        
        .nav-item small {
            font-size: 0.7em;
        }
        
        .section {
            display: none;
        }
        
        .section.active {
            display: block;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #7f8c8d;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            z-index: 2000;
            display: none;
            backdrop-filter: blur(10px);
        }
        
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #7f8c8d;
        }
        
        .empty-state span {
            font-size: 3em;
            display: block;
            margin-bottom: 10px;
            opacity: 0.5;
        }

        /* 加载页面样式 */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            text-align: center;
            color: white;
            max-width: 300px;
            padding: 20px;
        }

        .logo {
            font-size: 4em;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }

        .loading-content h2 {
            margin-bottom: 30px;
            font-weight: 300;
        }

        .loading-steps {
            margin-bottom: 30px;
        }

        .step {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0;
            opacity: 0.5;
            transition: all 0.5s ease;
        }

        .step.active {
            opacity: 1;
            transform: translateX(5px);
        }

        .step.completed {
            opacity: 0.7;
        }

        .step-icon {
            font-size: 1.2em;
            margin-right: 10px;
        }

        .step-text {
            flex: 1;
            text-align: left;
        }

        .step-status {
            font-size: 1.2em;
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: white;
            width: 0%;
            transition: width 0.5s ease;
        }

        /* 信息行样式 */
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .info-row:last-child {
            border-bottom: none;
        }

        .info-row span:first-child {
            font-weight: 500;
            color: #7f8c8d;
        }

        .info-row span:last-child {
            font-weight: 600;
            color: #2c3e50;
        }

        /* 控制按钮样式 */
        .task-controls, .app-controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
        }

        .task-controls .btn, .app-controls .btn {
            flex: 1;
        }

        /* 自定义复选框样式 */
        .custom-checkbox {
            position: absolute;
            top: 15px;
            left: 15px;
            width: 22px;
            height: 22px;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s ease;
        }

        .app-item.multi-select-mode .custom-checkbox {
            opacity: 1;
            transform: scale(1);
        }

        .custom-checkbox input {
            opacity: 0;
            position: absolute;
            cursor: pointer;
        }

        .custom-checkbox .checkmark {
            position: absolute;
            top: 0;
            left: 0;
            height: 22px;
            width: 22px;
            background-color: rgba(255, 255, 255, 0.9);
            border: 2px solid #ddd;
            border-radius: 6px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .custom-checkbox input:checked ~ .checkmark {
            background-color: #007AFF;
            border-color: #007AFF;
            transform: scale(1.1);
        }

        .custom-checkbox .checkmark:after {
            content: "";
            position: absolute;
            display: none;
            left: 7px;
            top: 3px;
            width: 6px;
            height: 10px;
            border: solid white;
            border-width: 0 2px 2px 0;
            transform: rotate(45deg);
        }

        .custom-checkbox input:checked ~ .checkmark:after {
            display: block;
        }

        /* 任务拖拽样式 */
        .task-item {
            cursor: move;
        }

        .task-item.dragging {
            opacity: 0.5;
            transform: rotate(5deg) scale(1.05);
            z-index: 1000;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }

        .task-item.drag-over {
            border-top: 3px solid #007AFF;
            transform: translateY(5px);
        }

        .task-delete-btn {
            background: #e74c3c;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            font-size: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .task-delete-btn:hover {
            background: #c0392b;
            transform: scale(1.1);
        }

        .drag-handle {
            color: #bdc3c7;
            cursor: grab;
            font-size: 16px;
            padding: 5px;
            margin-right: 10px;
        }

        .drag-handle:active {
            cursor: grabbing;
        }

        /* 复选框样式 */
        .checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid #667eea;
            border-radius: 4px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .checkbox.checked {
            background: #667eea;
            color: white;
        }

        .checkbox.checked::after {
            content: '✓';
            font-size: 12px;
        }
    </style>
</head>
<body>
    <!-- 启动加载页面 -->
    <div id="loadingScreen" class="loading-screen">
        <div class="loading-content">
            <div class="logo">🤖</div>
            <h2>自动化控制台</h2>
            <div class="loading-steps">
                <div class="step" id="step1">
                    <span class="step-icon">🔍</span>
                    <span class="step-text">检测设备环境...</span>
                    <span class="step-status">⏳</span>
                </div>
                <div class="step" id="step2">
                    <span class="step-icon">📱</span>
                    <span class="step-text">连接Android设备...</span>
                    <span class="step-status">⏳</span>
                </div>
                <div class="step" id="step3">
                    <span class="step-icon">🔧</span>
                    <span class="step-text">初始化脚本引擎...</span>
                    <span class="step-status">⏳</span>
                </div>
                <div class="step" id="step4">
                    <span class="step-icon">✨</span>
                    <span class="step-text">启动自动化服务...</span>
                    <span class="step-status">⏳</span>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>
    </div>

    <div class="container" id="mainContent" style="display: none;">
        <div class="header">
            <h1>🤖 移动端自动化控制台</h1>
            <p>设备信息 · 任务管理 · 应用下载</p>
        </div>

        <!-- 设备信息页面 -->
        <div id="deviceSection" class="section active">
            <div class="card">
                <h3>📱 设备信息</h3>
                <div id="deviceInfo">
                    <div class="info-row">
                        <span>设备型号:</span>
                        <span id="deviceModel">加载中...</span>
                    </div>
                    <div class="info-row">
                        <span>Android版本:</span>
                        <span id="androidVersion">加载中...</span>
                    </div>
                    <div class="info-row">
                        <span>屏幕分辨率:</span>
                        <span id="screenResolution">加载中...</span>
                    </div>
                    <div class="info-row">
                        <span>连接状态:</span>
                        <span id="connectionStatus">
                            <span class="status-indicator status-running"></span>已连接
                        </span>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>📊 服务状态</h3>
                <div id="serviceStatus">
                    <div class="status-row">
                        <span><span class="status-indicator status-running"></span>脚本引擎</span>
                        <span id="scriptEngineStatus">运行中</span>
                    </div>
                    <div class="status-row">
                        <span><span class="status-indicator status-running"></span>UI自动化</span>
                        <span id="uiAutoStatus">运行中</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 任务列表页面 -->
        <div id="taskSection" class="section">
            <div class="card">
                <h3>📋 任务列表</h3>
                <div class="task-controls">
                    <button class="btn" id="taskToggleBtn" onclick="toggleAllTasks()">启动所有任务</button>
                </div>
                <div id="taskList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>加载任务列表...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 应用管理页面 -->
        <div id="appSection" class="section">
            <div class="card">
                <h3>📱 应用下载</h3>

                <!-- 应用分类 -->
                <div class="app-categories">
                    <button class="category-btn active" onclick="filterApps('all')">全部</button>
                    <button class="category-btn" onclick="filterApps('system')">系统工具</button>
                    <button class="category-btn" onclick="filterApps('social')">社交通讯</button>
                    <button class="category-btn" onclick="filterApps('entertainment')">影音娱乐</button>
                    <button class="category-btn" onclick="filterApps('productivity')">办公学习</button>
                    <button class="category-btn" onclick="filterApps('games')">游戏</button>
                </div>

                <!-- 应用工具栏 -->
                <div class="app-toolbar">
                    <div class="left-actions">
                        <button class="toolbar-btn" onclick="toggleMultiSelectMode()">
                            <span id="multiSelectText">多选</span>
                        </button>
                        <button class="toolbar-btn" onclick="selectAllApps()" id="selectAllBtn" style="display: none;">全选</button>
                    </div>
                    <div class="right-actions">
                        <button class="toolbar-btn primary" onclick="downloadSelected()" id="downloadBtn" style="display: none;">下载选中</button>
                        <button class="toolbar-btn" onclick="addSelectedToTasks()" id="addToTaskBtn" style="display: none;">添加到任务</button>
                    </div>
                </div>

                <div id="appList">
                    <div class="loading">
                        <div class="spinner"></div>
                        <p>加载应用列表...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航 -->
    <div class="bottom-nav" id="bottomNav" style="display: none;">
        <div class="nav-item active" onclick="switchSection('device')">
            <span>📱</span>
            <small>设备</small>
        </div>
        <div class="nav-item" onclick="switchSection('task')">
            <span>📋</span>
            <small>任务</small>
        </div>
        <div class="nav-item" onclick="switchSection('app')">
            <span>📦</span>
            <small>下载</small>
        </div>
    </div>

    <!-- 提示消息 -->
    <div id="toast" class="toast"></div>

    <script>
        let serviceRunning = true; // 自动启动
        let tasks = [];
        let apps = [];
        let currentSection = 'device';
        let allTasksRunning = false;
        let selectedApps = new Set();
        let multiSelectMode = false;
        let currentCategory = 'all';
        let longPressTimer = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            startLoadingSequence();
        });

        // 启动加载序列
        async function startLoadingSequence() {
            const steps = [
                { id: 'step1', delay: 800 },
                { id: 'step2', delay: 1000 },
                { id: 'step3', delay: 1200 },
                { id: 'step4', delay: 1000 }
            ];

            let progress = 0;
            const progressFill = document.getElementById('progressFill');

            for (let i = 0; i < steps.length; i++) {
                const step = document.getElementById(steps[i].id);
                step.classList.add('active');

                await new Promise(resolve => setTimeout(resolve, steps[i].delay));

                step.classList.remove('active');
                step.classList.add('completed');
                step.querySelector('.step-status').textContent = '✅';

                progress = ((i + 1) / steps.length) * 100;
                progressFill.style.width = progress + '%';
            }

            // 启动服务
            await autoStartService();

            // 隐藏加载页面，显示主内容
            setTimeout(() => {
                document.getElementById('loadingScreen').style.display = 'none';
                document.getElementById('mainContent').style.display = 'block';
                document.getElementById('bottomNav').style.display = 'flex';

                // 初始化数据
                refreshDeviceInfo();
                refreshTasks();
                refreshApps();
            }, 500);
        }

        // 自动启动服务
        async function autoStartService() {
            try {
                const response = await fetch('/ui/start-u2', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                serviceRunning = result.success;
            } catch (error) {
                console.error('自动启动服务失败:', error);
                serviceRunning = true; // 假设启动成功
            }
        }

        // 显示提示消息
        function showToast(message, duration = 3000) {
            const toast = document.getElementById('toast');
            toast.textContent = message;
            toast.style.display = 'block';
            setTimeout(() => {
                toast.style.display = 'none';
            }, duration);
        }

        // 刷新设备信息
        async function refreshDeviceInfo() {
            try {
                const response = await fetch('/device/info');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('deviceModel').textContent = result.model || 'Unknown';
                    document.getElementById('androidVersion').textContent = result.android_version || 'Unknown';
                    document.getElementById('screenResolution').textContent = result.resolution || 'Unknown';
                } else {
                    // 使用模拟数据
                    document.getElementById('deviceModel').textContent = 'Xiaomi 12';
                    document.getElementById('androidVersion').textContent = 'Android 12';
                    document.getElementById('screenResolution').textContent = '1080x2400';
                }
            } catch (error) {
                console.error('获取设备信息失败:', error);
                // 使用模拟数据
                document.getElementById('deviceModel').textContent = 'Xiaomi 12';
                document.getElementById('androidVersion').textContent = 'Android 12';
                document.getElementById('screenResolution').textContent = '1080x2400';
            }
        }

        // 切换页面
        function switchSection(section) {
            // 隐藏所有页面
            document.querySelectorAll('.section').forEach(s => s.classList.remove('active'));
            document.querySelectorAll('.nav-item').forEach(n => n.classList.remove('active'));

            // 显示选中页面
            document.getElementById(section + 'Section').classList.add('active');
            event.target.closest('.nav-item').classList.add('active');

            currentSection = section;

            // 根据页面刷新数据
            if (section === 'task') {
                refreshTasks();
            } else if (section === 'app') {
                refreshApps();
            } else if (section === 'device') {
                refreshDeviceInfo();
            }
        }

        // 启动/停止所有任务
        async function toggleAllTasks() {
            const button = document.getElementById('taskToggleBtn');

            try {
                if (allTasksRunning) {
                    // 停止所有任务
                    for (let task of tasks) {
                        if (task.status === 'running') {
                            const response = await fetch('/script/stop', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ script_id: task.name })
                            });

                            if (response.ok) {
                                task.status = 'stopped';
                            }
                        }
                    }
                    allTasksRunning = false;
                    button.textContent = '启动所有任务';
                    button.className = 'btn btn-success';
                    showToast('所有任务已停止');
                } else {
                    // 启动所有任务
                    for (let task of tasks) {
                        if (task.status === 'stopped') {
                            const response = await fetch('/script/start', {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ script_path: task.name })
                            });

                            if (response.ok) {
                                task.status = 'running';
                            }
                        }
                    }
                    allTasksRunning = true;
                    button.textContent = '停止所有任务';
                    button.className = 'btn btn-danger';
                    showToast('所有任务已启动');
                }
                renderTasks();
            } catch (error) {
                console.error('批量操作任务失败:', error);
                showToast('操作失败，请检查网络连接');
            }
        }

        // 切换服务状态
        async function toggleService() {
            try {
                const endpoint = serviceRunning ? '/ui/stop-u2' : '/ui/start-u2';
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                if (result.success) {
                    serviceRunning = !serviceRunning;
                    updateServiceStatus();
                    showToast(serviceRunning ? '服务启动成功' : '服务停止成功');
                } else {
                    showToast('操作失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('服务切换失败:', error);
                showToast('网络错误，请检查连接');
            }
        }

        // 更新服务状态显示
        function updateServiceStatus() {
            const scriptStatus = document.getElementById('scriptEngineStatus');
            const uiStatus = document.getElementById('uiAutoStatus');
            const button = document.getElementById('serviceToggleBtn');
            const indicators = document.querySelectorAll('#serviceStatus .status-indicator');

            if (serviceRunning) {
                scriptStatus.textContent = '运行中';
                uiStatus.textContent = '运行中';
                button.textContent = '停止服务';
                button.className = 'btn btn-danger';
                indicators.forEach(indicator => {
                    indicator.className = 'status-indicator status-running';
                });
            } else {
                scriptStatus.textContent = '已停止';
                uiStatus.textContent = '已停止';
                button.textContent = '启动服务';
                button.className = 'btn';
                indicators.forEach(indicator => {
                    indicator.className = 'status-indicator status-stopped';
                });
            }
        }

        // 刷新服务状态
        async function refreshServiceStatus() {
            try {
                const response = await fetch('/ui/service-status');
                const result = await response.json();
                serviceRunning = result.success && result.running;
                updateServiceStatus();
            } catch (error) {
                console.error('获取服务状态失败:', error);
                updateServiceStatus(); // 使用默认状态
            }
        }

        // 截图功能
        async function takeScreenshot() {
            try {
                showToast('正在截图...');
                const response = await fetch('/screenshot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                if (result.success) {
                    showToast('截图成功: ' + result.filename);
                } else {
                    showToast('截图失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('截图失败:', error);
                showToast('截图失败，请检查网络连接');
            }
        }

        // 刷新所有数据
        function refreshAll() {
            showToast('正在刷新...');
            refreshServiceStatus();
            refreshTasks();
            refreshApps();
            showToast('刷新完成');
        }

        // 刷新任务列表
        async function refreshTasks() {
            const taskListDiv = document.getElementById('taskList');
            taskListDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>加载任务列表...</p></div>';

            try {
                const response = await fetch('/script/list');
                const result = await response.json();

                if (result.success && result.scripts) {
                    tasks = result.scripts;
                } else {
                    // 使用示例数据
                    tasks = [
                        { name: '抖音自动点赞', status: 'stopped', app: 'com.ss.android.ugc.aweme', description: '自动为视频点赞' },
                        { name: '微信自动回复', status: 'running', app: 'com.tencent.mm', description: '自动回复消息' },
                        { name: '淘宝签到', status: 'pending', app: 'com.taobao.taobao', description: '每日自动签到' },
                        { name: '支付宝蚂蚁森林', status: 'stopped', app: 'com.eg.android.AlipayGphone', description: '收取能量' }
                    ];
                }
                renderTasks();
            } catch (error) {
                console.error('获取任务列表失败:', error);
                // 显示示例数据
                tasks = [
                    { name: '抖音自动点赞', status: 'stopped', app: 'com.ss.android.ugc.aweme', description: '自动为视频点赞' },
                    { name: '微信自动回复', status: 'running', app: 'com.tencent.mm', description: '自动回复消息' },
                    { name: '淘宝签到', status: 'pending', app: 'com.taobao.taobao', description: '每日自动签到' }
                ];
                renderTasks();
            }
        }

        // 渲染任务列表
        function renderTasks() {
            const taskListDiv = document.getElementById('taskList');

            if (tasks.length === 0) {
                taskListDiv.innerHTML = `
                    <div class="empty-state">
                        <span>📋</span>
                        <p>暂无任务</p>
                        <small>从应用下载页面添加应用到任务列表</small>
                    </div>
                `;
                return;
            }

            // 更新全局按钮状态
            const runningTasks = tasks.filter(t => t.status === 'running').length;
            allTasksRunning = runningTasks > 0;
            const button = document.getElementById('taskToggleBtn');
            if (allTasksRunning) {
                button.textContent = '停止所有任务';
                button.className = 'btn btn-danger';
            } else {
                button.textContent = '启动所有任务';
                button.className = 'btn btn-success';
            }

            taskListDiv.innerHTML = tasks.map((task, index) => `
                <div class="task-item"
                     draggable="true"
                     data-index="${index}"
                     ondragstart="handleDragStart(event)"
                     ondragover="handleDragOver(event)"
                     ondrop="handleDrop(event)"
                     ondragend="handleDragEnd(event)">
                    <div class="item-header">
                        <div style="display: flex; align-items: center;">
                            <div class="drag-handle">⋮⋮</div>
                            <div>
                                <div class="item-title">${task.name}</div>
                                <div class="item-subtitle">${task.description || task.app}</div>
                            </div>
                        </div>
                        <div class="item-actions">
                            <span class="status-indicator status-${task.status}"></span>
                            <span style="font-size: 0.9em; color: #7f8c8d; margin-right: 10px;">
                                ${task.status === 'running' ? '运行中' : task.status === 'pending' ? '等待中' : '已停止'}
                            </span>
                            <button class="task-delete-btn" onclick="deleteTask(${index})" title="删除任务">
                                ×
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function getTaskButtonClass(status) {
            switch(status) {
                case 'running': return 'btn-danger';
                case 'pending': return 'btn-warning';
                default: return 'btn-success';
            }
        }

        function getTaskButtonText(status) {
            switch(status) {
                case 'running': return '停止';
                case 'pending': return '等待';
                default: return '启动';
            }
        }

        // 拖拽相关变量
        let draggedElement = null;
        let draggedIndex = null;

        // 拖拽开始
        function handleDragStart(event) {
            draggedElement = event.target;
            draggedIndex = parseInt(event.target.dataset.index);
            event.target.classList.add('dragging');
            event.dataTransfer.effectAllowed = 'move';
            event.dataTransfer.setData('text/html', event.target.outerHTML);
        }

        // 拖拽经过
        function handleDragOver(event) {
            event.preventDefault();
            event.dataTransfer.dropEffect = 'move';

            const afterElement = getDragAfterElement(event.currentTarget.parentNode, event.clientY);
            const dragging = document.querySelector('.dragging');

            if (afterElement == null) {
                event.currentTarget.parentNode.appendChild(dragging);
            } else {
                event.currentTarget.parentNode.insertBefore(dragging, afterElement);
            }
        }

        // 拖拽放下
        function handleDrop(event) {
            event.preventDefault();
            const targetIndex = parseInt(event.target.closest('.task-item').dataset.index);

            if (draggedIndex !== null && targetIndex !== null && draggedIndex !== targetIndex) {
                // 重新排序任务数组
                const draggedTask = tasks[draggedIndex];
                tasks.splice(draggedIndex, 1);
                tasks.splice(targetIndex, 0, draggedTask);

                // 重新渲染
                renderTasks();
                showToast('任务顺序已调整');
            }
        }

        // 拖拽结束
        function handleDragEnd(event) {
            event.target.classList.remove('dragging');
            draggedElement = null;
            draggedIndex = null;

            // 清除所有拖拽样式
            document.querySelectorAll('.task-item').forEach(item => {
                item.classList.remove('drag-over');
            });
        }

        // 获取拖拽后的位置
        function getDragAfterElement(container, y) {
            const draggableElements = [...container.querySelectorAll('.task-item:not(.dragging)')];

            return draggableElements.reduce((closest, child) => {
                const box = child.getBoundingClientRect();
                const offset = y - box.top - box.height / 2;

                if (offset < 0 && offset > closest.offset) {
                    return { offset: offset, element: child };
                } else {
                    return closest;
                }
            }, { offset: Number.NEGATIVE_INFINITY }).element;
        }

        // 删除任务
        function deleteTask(index) {
            if (confirm('确定要删除这个任务吗？')) {
                const taskName = tasks[index].name;
                tasks.splice(index, 1);
                renderTasks();
                showToast(`任务"${taskName}"已删除`);
            }
        }

        // 切换任务状态
        async function toggleTask(taskName) {
            const task = tasks.find(t => t.name === taskName);
            if (!task) return;

            try {
                if (task.status === 'running') {
                    // 停止任务
                    const response = await fetch('/script/stop', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ script_id: taskName })
                    });

                    const result = await response.json();
                    if (result.success) {
                        task.status = 'stopped';
                        showToast('任务已停止');
                    } else {
                        showToast('停止失败: ' + (result.error || '未知错误'));
                    }
                } else {
                    // 启动任务
                    const response = await fetch('/script/start', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ script_path: taskName })
                    });

                    const result = await response.json();
                    if (result.success) {
                        task.status = 'running';
                        showToast('任务已启动');
                    } else {
                        showToast('启动失败: ' + (result.error || '未知错误'));
                    }
                }
                renderTasks();
            } catch (error) {
                console.error('任务操作失败:', error);
                showToast('操作失败，请检查网络连接');
            }
        }

        // 切换多选模式
        function toggleMultiSelectMode() {
            multiSelectMode = !multiSelectMode;
            const multiSelectText = document.getElementById('multiSelectText');
            const selectAllBtn = document.getElementById('selectAllBtn');
            const downloadBtn = document.getElementById('downloadBtn');
            const addToTaskBtn = document.getElementById('addToTaskBtn');

            if (multiSelectMode) {
                multiSelectText.textContent = '取消';
                selectAllBtn.style.display = 'block';
                downloadBtn.style.display = 'block';
                addToTaskBtn.style.display = 'block';
            } else {
                multiSelectText.textContent = '多选';
                selectAllBtn.style.display = 'none';
                downloadBtn.style.display = 'none';
                addToTaskBtn.style.display = 'none';
                selectedApps.clear();
            }

            renderApps();
        }

        // 全选应用
        function selectAllApps() {
            const filteredApps = getFilteredApps();
            if (selectedApps.size === filteredApps.length) {
                selectedApps.clear();
            } else {
                filteredApps.forEach(app => selectedApps.add(app.package));
            }
            renderApps();
        }

        // 应用分类过滤
        function filterApps(category) {
            currentCategory = category;

            // 更新分类按钮状态
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            renderApps();
        }

        // 获取过滤后的应用列表
        function getFilteredApps() {
            if (currentCategory === 'all') {
                return apps;
            }
            return apps.filter(app => app.category === currentCategory);
        }

        // 长按进入多选模式
        function handleAppLongPress(appElement, packageName) {
            longPressTimer = setTimeout(() => {
                if (!multiSelectMode) {
                    toggleMultiSelectMode();
                    selectedApps.add(packageName);
                    renderApps();
                    navigator.vibrate && navigator.vibrate(50); // 震动反馈
                }
            }, 500);
        }

        // 取消长按
        function cancelLongPress() {
            if (longPressTimer) {
                clearTimeout(longPressTimer);
                longPressTimer = null;
            }
        }

        // 刷新应用列表
        async function refreshApps() {
            const appListDiv = document.getElementById('appList');
            appListDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>加载应用列表...</p></div>';

            // 模拟获取可下载应用列表
            setTimeout(() => {
                apps = [
                    { name: '抖音', package: 'com.ss.android.ugc.aweme', version: '21.0.0', downloaded: true, inTask: true, icon: '🎵', size: '156MB', category: 'entertainment' },
                    { name: '微信', package: 'com.tencent.mm', version: '8.0.28', downloaded: true, inTask: true, icon: '💬', size: '245MB', category: 'social' },
                    { name: '淘宝', package: 'com.taobao.taobao', version: '10.5.0', downloaded: true, inTask: true, icon: '🛒', size: '189MB', category: 'productivity' },
                    { name: '支付宝', package: 'com.eg.android.AlipayGphone', version: '10.2.96', downloaded: false, inTask: false, icon: '💰', size: '134MB', category: 'productivity' },
                    { name: '京东', package: 'com.jingdong.app.mall', version: '11.1.0', downloaded: false, inTask: false, icon: '🛍️', size: '167MB', category: 'productivity' },
                    { name: '拼多多', package: 'com.xunmeng.pinduoduo', version: '6.29.1', downloaded: false, inTask: false, icon: '🍎', size: '98MB', category: 'productivity' },
                    { name: '快手', package: 'com.smile.gifmaker', version: '10.1.20', downloaded: false, inTask: false, icon: '📹', size: '123MB', category: 'entertainment' },
                    { name: '小红书', package: 'com.xingin.xhs', version: '7.56.1', downloaded: false, inTask: false, icon: '📖', size: '145MB', category: 'social' },
                    { name: '美团', package: 'com.sankuai.meituan', version: '12.4.0', downloaded: false, inTask: false, icon: '🍔', size: '178MB', category: 'productivity' },
                    { name: '饿了么', package: 'me.ele.android', version: '10.8.5', downloaded: false, inTask: false, icon: '🥡', size: '112MB', category: 'productivity' },
                    { name: '王者荣耀', package: 'com.tencent.tmgp.sgame', version: '3.71.1.8', downloaded: false, inTask: false, icon: '⚔️', size: '1.8GB', category: 'games' },
                    { name: '和平精英', package: 'com.tencent.tmgp.pubgmhd', version: '1.15.12', downloaded: false, inTask: false, icon: '🔫', size: '2.1GB', category: 'games' },
                    { name: 'QQ', package: 'com.tencent.mobileqq', version: '8.9.15', downloaded: false, inTask: false, icon: '🐧', size: '178MB', category: 'social' },
                    { name: '网易云音乐', package: 'com.netease.cloudmusic', version: '8.7.40', downloaded: false, inTask: false, icon: '🎶', size: '145MB', category: 'entertainment' },
                    { name: '高德地图', package: 'com.autonavi.minimap', version: '11.15.0', downloaded: false, inTask: false, icon: '🗺️', size: '178MB', category: 'system' }
                ];
                selectedApps.clear();
                renderApps();
            }, 1000);
        }

        // 渲染应用列表
        function renderApps() {
            const appListDiv = document.getElementById('appList');
            const filteredApps = getFilteredApps();

            if (filteredApps.length === 0) {
                appListDiv.innerHTML = `
                    <div class="empty-state">
                        <span>📱</span>
                        <p>暂无应用</p>
                        <small>${currentCategory === 'all' ? '请检查网络连接' : '该分类下暂无应用'}</small>
                    </div>
                `;
                return;
            }

            appListDiv.innerHTML = filteredApps.map(app => `
                <div class="app-item ${multiSelectMode ? 'multi-select-mode' : ''} ${selectedApps.has(app.package) ? 'selected' : ''}"
                     ontouchstart="handleAppLongPress(this, '${app.package}')"
                     ontouchend="cancelLongPress()"
                     ontouchmove="cancelLongPress()"
                     onclick="${multiSelectMode ? `toggleAppSelection('${app.package}')` : ''}">

                    ${multiSelectMode ? `
                        <label class="custom-checkbox">
                            <input type="checkbox" ${selectedApps.has(app.package) ? 'checked' : ''}
                                   onchange="toggleAppSelection('${app.package}')">
                            <span class="checkmark"></span>
                        </label>
                    ` : ''}

                    <div class="item-header">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <div>
                                <div class="item-title">${app.icon} ${app.name}</div>
                                <div class="item-subtitle">${app.size} · 版本 ${app.version}</div>
                                <div class="item-subtitle" style="color: ${app.downloaded ? '#2ecc71' : '#e67e22'};">
                                    ${app.downloaded ? '✅ 已下载' : '📥 未下载'}
                                    ${app.inTask ? ' · 已添加到任务' : ''}
                                </div>
                            </div>
                        </div>
                        ${!multiSelectMode ? `
                            <div class="item-actions">
                                ${!app.downloaded ? '<button class="btn btn-small btn-primary" onclick="downloadApp(\'' + app.package + '\')">下载</button>' : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            `).join('');
        }

        // 切换应用选择状态
        function toggleAppSelection(packageName) {
            if (selectedApps.has(packageName)) {
                selectedApps.delete(packageName);
            } else {
                selectedApps.add(packageName);
            }
            renderApps();
        }

        // 下载选中的应用
        async function downloadSelected() {
            const selectedPackages = Array.from(selectedApps);
            if (selectedPackages.length === 0) {
                showToast('请先选择要下载的应用');
                return;
            }

            showToast(`开始下载 ${selectedPackages.length} 个应用...`);

            // 模拟下载过程
            for (let packageName of selectedPackages) {
                const app = apps.find(a => a.package === packageName);
                if (app && !app.downloaded) {
                    // 模拟下载延迟
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    app.downloaded = true;
                    showToast(`${app.name} 下载完成`);
                }
            }

            selectedApps.clear();
            renderApps();
            showToast('所有应用下载完成');
        }

        // 添加选中的应用到任务列表
        function addSelectedToTasks() {
            const selectedPackages = Array.from(selectedApps);
            if (selectedPackages.length === 0) {
                showToast('请先选择要添加的应用');
                return;
            }

            let addedCount = 0;
            for (let packageName of selectedPackages) {
                const app = apps.find(a => a.package === packageName);
                if (app && app.downloaded && !app.inTask) {
                    app.inTask = true;
                    const newTask = {
                        name: `${app.name}自动化`,
                        status: 'stopped',
                        app: app.package,
                        description: `${app.name}自动化脚本`
                    };
                    tasks.push(newTask);
                    addedCount++;
                }
            }

            if (addedCount > 0) {
                showToast(`成功添加 ${addedCount} 个应用到任务列表`);
                selectedApps.clear();
                renderApps();
                if (currentSection === 'task') {
                    renderTasks();
                }
            } else {
                showToast('没有可添加的应用（需要先下载且未添加过）');
            }
        }

        // 切换应用在任务列表中的状态
        function toggleAppInTask(packageName) {
            const app = apps.find(a => a.package === packageName);
            if (!app) return;

            app.inTask = !app.inTask;

            if (app.inTask) {
                // 添加到任务列表
                const newTask = {
                    name: `${app.name}自动化`,
                    status: 'stopped',
                    app: app.package,
                    description: `${app.name}自动化脚本`
                };
                tasks.push(newTask);
                showToast(`${app.name} 已添加到任务列表`);
            } else {
                // 从任务列表移除
                tasks = tasks.filter(task => task.app !== app.package);
                showToast(`${app.name} 已从任务列表移除`);
            }

            renderApps();
            if (currentSection === 'task') {
                renderTasks();
            }
        }

        // 定期刷新状态
        setInterval(() => {
            if (currentSection === 'status') {
                refreshServiceStatus();
            }
        }, 5000);

        // 定期刷新状态
        setInterval(() => {
            if (currentSection === 'device') {
                refreshDeviceInfo();
            }
        }, 10000); // 每10秒刷新一次设备信息
    </script>
</body>
</html>
