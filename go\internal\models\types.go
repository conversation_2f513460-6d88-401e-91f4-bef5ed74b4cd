package models

import "time"

// Response 通用响应结构体
type Response struct {
	Success bool        `json:"success"`
	Message string      `json:"message,omitempty"`
	Error   string      `json:"error,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// ScreenshotResponse 截图响应
type ScreenshotResponse struct {
	Success  bool   `json:"success"`
	Filename string `json:"filename,omitempty"`
	URL      string `json:"url,omitempty"`
	Error    string `json:"error,omitempty"`
}

// ClickRequest 点击请求
type ClickRequest struct {
	X int `json:"x" binding:"required"`
	Y int `json:"y" binding:"required"`
}

// SwipeRequest 滑动请求
type SwipeRequest struct {
	X1       int `json:"x1" binding:"required"`
	Y1       int `json:"y1" binding:"required"`
	X2       int `json:"x2" binding:"required"`
	Y2       int `json:"y2" binding:"required"`
	Duration int `json:"duration" binding:"required"`
}

// OCRRequest OCR请求
type OCRRequest struct{}

// OCRClickRequest OCR点击请求
type OCRClickRequest struct {
	Text   string `json:"text" binding:"required"`
	Method string `json:"method,omitempty"`
}

// OCRClickResponse OCR点击响应
type OCRClickResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	ClickX    int         `json:"click_x,omitempty"`
	ClickY    int         `json:"click_y,omitempty"`
	OCRResult interface{} `json:"ocr_result,omitempty"`
	Error     string      `json:"error,omitempty"`
}

// OCRSearchResult OCR搜索结果
type OCRSearchResult struct {
	Text   string `json:"text"`
	X      int    `json:"x"`
	Y      int    `json:"y"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

// UI自动化相关结构体
type UIMatchRequest struct {
	Template map[string]string `json:"template" binding:"required"`
}

type UIFingerprintMatchRequest struct {
	Fingerprint string `json:"fingerprint" binding:"required"`
}

type UIMatchResponse struct {
	Success bool        `json:"success"`
	Result  interface{} `json:"result,omitempty"`
	Error   string      `json:"error,omitempty"`
}

type U2ServiceResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message,omitempty"`
	Error   string `json:"error,omitempty"`
}

// 脚本管理相关结构体
type StartScriptRequest struct {
	ScriptPath string `json:"script_path" binding:"required"`
}

type StopScriptRequest struct {
	ScriptID string `json:"script_id" binding:"required"`
}

type ScriptStatusResponse struct {
	Success bool                    `json:"success"`
	Scripts map[string]ScriptStatus `json:"scripts,omitempty"`
	Error   string                  `json:"error,omitempty"`
}

type ScriptStatus struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Status      string    `json:"status"`
	StartTime   time.Time `json:"start_time"`
	RunningTime string    `json:"running_time"`
}

// UI指纹相关结构体
type UIFingerprintGenerateRequest struct {
	X int `json:"x" binding:"required"`
	Y int `json:"y" binding:"required"`
}

type UIFingerprintResponse struct {
	Success     bool        `json:"success"`
	Fingerprint interface{} `json:"fingerprint,omitempty"`
	Error       string      `json:"error,omitempty"`
}
