<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能脚本制作器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            display: flex;
            height: 100vh;
            gap: 10px;
            padding: 10px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }

        .left-panel {
            width: 400px;
            display: flex;
            flex-direction: column;
        }

        .center-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .right-panel {
            width: 350px;
            display: flex;
            flex-direction: column;
        }

        .panel-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            font-weight: bold;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        .device-preview {
            position: relative;
            background: #000;
            border-radius: 10px;
            margin-bottom: 15px;
            overflow: hidden;
            aspect-ratio: 9/16;
            max-height: 600px;
        }

        .device-screen {
            width: 100%;
            height: 100%;
            object-fit: contain;
            cursor: crosshair;
            background: #f8f9fa; /* 避免黑边 */
            border-radius: 8px;
        }

        .overlay-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        .ui-element {
            position: absolute;
            cursor: pointer;
            pointer-events: all;
            box-sizing: border-box;
            border-width: 1px;
            border-style: solid;
            background: transparent !important; /* 完全透明背景 */
        }

        /* 按钮类元素 - 绿色 */
        .ui-element.button {
            border-color: rgba(0, 255, 0, 0.6);
        }

        /* 文本输入框 - 橙色 */
        .ui-element.edittext {
            border-color: rgba(255, 165, 0, 0.6);
        }

        /* 文本显示 - 青色 */
        .ui-element.textview {
            border-color: rgba(0, 255, 255, 0.6);
        }

        /* 图片元素 - 洋红色 */
        .ui-element.image {
            border-color: rgba(255, 0, 255, 0.6);
        }

        /* 布局容器 - 黄色 */
        .ui-element.layout {
            border-color: rgba(255, 255, 0, 0.6);
        }

        /* 可点击元素 - 红色外框 */
        .ui-element.clickable {
            border-color: rgba(255, 0, 0, 0.6);
            box-shadow: inset 0 0 0 1px rgba(255, 0, 0, 0.4);
        }

        /* 默认元素 - 浅蓝色 */
        .ui-element.default {
            border-color: rgba(0, 100, 255, 0.6);
        }

        .ui-element:hover {
            border-width: 2px;
            opacity: 0.8;
        }

        .ui-element.selected {
            border-color: red !important;
            border-width: 2px;
            box-shadow: 0 0 0 1px red;
        }

        .ocr-text {
            position: absolute;
            border: 2px solid #3742fa;
            background: rgba(55, 66, 250, 0.1);
            cursor: pointer;
            pointer-events: all;
            transition: all 0.2s;
            font-size: 12px;
            color: #3742fa;
            padding: 2px 4px;
        }

        .ocr-text:hover {
            background: rgba(55, 66, 250, 0.3);
            border-color: #2f3542;
        }

        .ocr-text.selected {
            border-color: #2ed573;
            background: rgba(46, 213, 115, 0.2);
        }

        .selected-element {
            border: 3px solid #ff0000 !important;
            background-color: rgba(255, 0, 0, 0.2) !important;
            z-index: 1000;
            box-sizing: border-box;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ed573 0%, #17a2b8 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .action-list {
            max-height: 400px;
            overflow-y: auto;
        }

        .action-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .action-item:hover {
            background: #e9ecef;
            transform: translateX(5px);
        }

        .action-item.selected {
            background: #e3f2fd;
            border-color: #2196f3;
        }

        .action-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 8px;
        }

        .action-type {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .action-name {
            font-weight: 500;
            color: #333;
            flex: 1;
            margin-left: 10px;
        }

        .action-controls {
            display: flex;
            gap: 5px;
        }

        .status-bar {
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 10px 20px;
            font-size: 14px;
            color: #666;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            background: transparent;
            border: none;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
        }

        .tab.active {
            background: #667eea;
            color: white;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .element-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .element-info h4 {
            margin-bottom: 10px;
            color: #333;
        }

        .element-property {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }

        .element-property .key {
            font-weight: 500;
            color: #666;
        }

        .element-property .value {
            color: #333;
            word-break: break-all;
        }

        .flow-tabs {
            display: flex;
            margin-bottom: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            overflow: hidden;
        }

        .flow-tab {
            flex: 1;
            padding: 8px 12px;
            text-align: center;
            cursor: pointer;
            background: transparent;
            border: none;
            font-size: 13px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s;
        }

        .flow-tab.active {
            background: #667eea;
            color: white;
        }

        .script-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 左侧面板：设备预览和元素选择 -->
        <div class="panel left-panel">
            <div class="panel-header">
                📱 设备预览与元素选择
            </div>
            <div class="panel-content">
                <div class="form-group" id="serverConfig" style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-bottom: 15px;">
                    <label style="font-weight: bold; color: #856404;">📡 服务器地址配置:</label>
                    <div style="display: flex; gap: 5px; margin-top: 8px;">
                        <input type="text" class="form-control" id="serverAddress" placeholder="http://**************:9088" value="http://**************:9088" style="flex: 1;">
                        <button class="btn btn-small" onclick="testConnection()">🔗 测试</button>
                        <button class="btn btn-small btn-success" onclick="setServerAddress()">✅ 设置</button>
                    </div>
                    <div style="display: flex; gap: 5px; margin-top: 5px;">
                        <button class="btn btn-small btn-warning" onclick="testBasicConnection()">🌐 基础测试</button>
                        <button class="btn btn-small btn-warning" onclick="openInNewTab()">🔗 浏览器打开</button>
                        <button class="btn btn-small" onclick="testWithCORS()">🔧 CORS测试</button>
                    </div>
                    <div style="font-size: 12px; color: #856404; margin-top: 5px;">
                        请输入手机上运行的服务器地址，例如: http://*************:8080<br>
                        💡 提示：在手机上运行 <code>./main_android_arm64</code> 启动服务器，然后查看输出的IP地址
                    </div>
                    <div id="connectionStatus" style="margin-top: 8px; font-size: 13px;"></div>
                </div>

                <div class="form-group">
                    <button class="btn btn-success" onclick="captureScreen()">📸 刷新截图</button>
                    <button class="btn btn-warning" onclick="analyzeUI()">🔍 分析UI元素</button>
                    <button class="btn btn-warning" onclick="analyzeOCR()">📝 分析OCR文字</button>

                </div>
                
                <div class="device-preview">
                    <img id="deviceScreen" class="device-screen" src="" alt="设备截图" onclick="onScreenClick(event)">
                    <div class="overlay-elements" id="overlayElements"></div>
                </div>

                <div class="tabs">
                    <button class="tab active" onclick="switchTab('ui')">UI元素</button>
                    <button class="tab" onclick="switchTab('ocr')">OCR文字</button>
                </div>

                <div id="uiTab" class="tab-content active">
                    <div id="uiElements"></div>
                </div>

                <div id="ocrTab" class="tab-content">
                    <div id="ocrTexts"></div>
                </div>
            </div>
        </div>

        <!-- 中间面板：动作配置 -->
        <div class="panel center-panel">
            <div class="panel-header">
                ⚙️ 动作配置
            </div>
            <div class="panel-content">
                <div class="element-info" id="selectedElementInfo" style="display: none;">
                    <h4>选中元素信息</h4>
                    <div id="elementDetails"></div>
                    <button class="btn btn-success" onclick="createActionFromElement()">➕ 创建动作</button>
                </div>

                <div class="form-group">
                    <label>动作类型:</label>
                    <select class="form-control" id="actionType" onchange="updateActionForm()">
                        <option value="click">点击</option>
                        <option value="ocr_click">OCR点击</option>
                        <option value="ui_click">UI点击</option>
                        <option value="swipe">滑动</option>
                        <option value="input">输入文字</option>
                        <option value="wait">等待</option>
                        <option value="app_launch">启动应用</option>
                        <option value="multi_target">多条件匹配</option>
                    </select>
                </div>

                <div class="form-group">
                    <label>动作名称:</label>
                    <input type="text" class="form-control" id="actionName" placeholder="输入动作名称">
                </div>

                <div id="actionForm">
                    <!-- 动态生成的表单内容 -->
                </div>

                <div class="form-group">
                    <button class="btn btn-success" onclick="addAction()">➕ 添加动作</button>
                    <button class="btn btn-warning" onclick="testCoordinate()">🎯 测试坐标</button>
                </div>
            </div>
        </div>

        <!-- 右侧面板：脚本管理 -->
        <div class="panel right-panel">
            <div class="panel-header">
                📋 脚本管理
            </div>
            <div class="panel-content">
                <div class="script-info">
                    <div class="form-group">
                        <label>脚本名称:</label>
                        <input type="text" class="form-control" id="scriptName" placeholder="输入脚本名称">
                    </div>
                    <div class="form-group">
                        <label>脚本描述:</label>
                        <textarea class="form-control" id="scriptDesc" rows="2" placeholder="输入脚本描述"></textarea>
                    </div>
                    <div class="form-group">
                        <label>目标应用包名:</label>
                        <input type="text" class="form-control" id="targetApp" placeholder="com.example.app">
                    </div>
                </div>

                <div class="flow-tabs">
                    <button class="flow-tab active" onclick="switchFlow('sequential')">顺序</button>
                    <button class="flow-tab" onclick="switchFlow('parallel')">并行</button>
                    <button class="flow-tab" onclick="switchFlow('global')">公共</button>
                </div>

                <div class="action-list" id="actionList">
                    <!-- 动作列表 -->
                </div>

                <div class="form-group">
                    <button class="btn btn-success" onclick="saveScript()">💾 保存脚本</button>
                    <button class="btn btn-warning" onclick="previewScript()">👁️ 预览JSON</button>
                    <button class="btn btn-danger" onclick="clearScript()">🗑️ 清空脚本</button>
                </div>

                <div class="form-group">
                    <button class="btn" onclick="runScript()">▶️ 运行脚本</button>
                    <button class="btn btn-danger" onclick="stopScript()">⏹️ 停止脚本</button>
                </div>
            </div>
            <div class="status-bar" id="statusBar">
                就绪
            </div>

            <!-- 调试面板 -->
            <div style="background: #f8f9fa; border-top: 1px solid #e9ecef; padding: 10px; font-size: 12px; max-height: 100px; overflow-y: auto;" id="debugPanel">
                <div style="font-weight: bold; margin-bottom: 5px;">🔧 调试信息:</div>
                <div id="debugLog">打开浏览器控制台查看详细日志</div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'ui';
        let currentFlow = 'sequential';
        let selectedElement = null;
        let uiElements = [];
        let ocrTexts = [];
        let scriptData = {
            sequential: [],
            parallel: [],
            global: []
        };
        let screenScale = 1;
        let currentDisplayMode = 'none'; // 'ui', 'ocr', 'none'
        let testMarker = null; // 测试坐标标记

        // API基础URL - 手机服务器地址
        let API_BASE = 'http://**************:9088';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateActionForm();
            loadSavedServerAddress();
        });

        // 显示服务器配置提示
        function showServerConfig() {
            document.getElementById('serverConfig').style.display = 'block';
        }

        // 加载保存的服务器地址
        function loadSavedServerAddress() {
            const saved = localStorage.getItem('serverAddress') || API_BASE;
            API_BASE = saved;
            document.getElementById('serverAddress').value = saved;

            // 自动测试连接
            testConnection().then(connected => {
                if (connected) {
                    document.getElementById('serverConfig').style.display = 'none';
                    // 自动截图
                    setTimeout(() => {
                        captureScreen();
                    }, 500);
                } else {
                    document.getElementById('serverConfig').style.display = 'block';
                    setStatus('服务器连接失败，请检查地址');
                }
            });
        }

        // 测试连接
        async function testConnection() {
            const address = document.getElementById('serverAddress').value.trim();
            if (!address) {
                updateConnectionStatus('请输入服务器地址', 'error');
                return false;
            }

            updateConnectionStatus('正在测试连接...', 'testing');
            console.log('测试连接到:', address);

            try {
                const response = await fetch(address + '/screenshot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({}),
                    signal: AbortSignal.timeout(8000) // 8秒超时
                });

                console.log('响应状态:', response.status);
                console.log('响应头:', response.headers);

                if (response.ok) {
                    const result = await response.json();
                    console.log('响应结果:', result);
                    updateConnectionStatus('✅ 连接成功！', 'success');
                    return true;
                } else {
                    const errorText = await response.text();
                    console.log('错误响应:', errorText);
                    updateConnectionStatus(`❌ 服务器响应错误: ${response.status} - ${errorText}`, 'error');
                    return false;
                }
            } catch (error) {
                console.error('连接错误:', error);
                if (error.name === 'AbortError') {
                    updateConnectionStatus('❌ 连接超时，请检查服务器地址和网络', 'error');
                } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                    updateConnectionStatus('❌ 网络错误：无法连接到服务器，请检查IP地址和端口', 'error');
                } else {
                    updateConnectionStatus('❌ 连接失败: ' + error.message, 'error');
                }
                return false;
            }
        }

        // 更新连接状态显示
        function updateConnectionStatus(message, type) {
            const statusDiv = document.getElementById('connectionStatus');
            statusDiv.textContent = message;

            // 移除所有状态类
            statusDiv.className = '';

            // 添加对应的状态类
            switch (type) {
                case 'success':
                    statusDiv.style.color = '#28a745';
                    break;
                case 'error':
                    statusDiv.style.color = '#dc3545';
                    break;
                case 'testing':
                    statusDiv.style.color = '#007bff';
                    break;
                default:
                    statusDiv.style.color = '#6c757d';
            }
        }

        // 设置服务器地址
        async function setServerAddress() {
            const address = document.getElementById('serverAddress').value.trim();
            if (!address) {
                updateConnectionStatus('请输入服务器地址', 'error');
                return;
            }

            // 测试连接
            const connected = await testConnection();
            if (connected) {
                API_BASE = address;
                localStorage.setItem('serverAddress', address);
                document.getElementById('serverConfig').style.display = 'none';
                setStatus('服务器地址已设置: ' + address);

                // 自动截图
                setTimeout(() => {
                    captureScreen();
                }, 1000);
            }
        }

        // 基础连接测试
        async function testBasicConnection() {
            const address = document.getElementById('serverAddress').value.trim();
            if (!address) {
                updateConnectionStatus('请输入服务器地址', 'error');
                return;
            }

            updateConnectionStatus('正在进行基础连接测试...', 'testing');
            addDebugLog(`基础测试: ${address}`);

            try {
                // 尝试简单的GET请求到根路径
                const response = await fetch(address + '/', {
                    method: 'GET',
                    mode: 'cors',
                    signal: AbortSignal.timeout(5000)
                });

                addDebugLog(`基础测试响应状态: ${response.status}`);

                if (response.ok) {
                    updateConnectionStatus('✅ 基础连接成功！服务器可访问', 'success');
                    return true;
                } else {
                    updateConnectionStatus(`⚠️ 服务器响应: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                addDebugLog(`基础测试错误: ${error.message}`);
                if (error.name === 'AbortError') {
                    updateConnectionStatus('❌ 基础连接超时', 'error');
                } else if (error.message.includes('CORS')) {
                    updateConnectionStatus('❌ CORS跨域问题，但服务器可能正常运行', 'error');
                } else {
                    updateConnectionStatus('❌ 基础连接失败: ' + error.message, 'error');
                }
                return false;
            }
        }

        // 在新标签页打开服务器地址
        function openInNewTab() {
            const address = document.getElementById('serverAddress').value.trim();
            if (!address) {
                updateConnectionStatus('请输入服务器地址', 'error');
                return;
            }

            addDebugLog(`在新标签页打开: ${address}`);
            window.open(address, '_blank');
            updateConnectionStatus('已在新标签页打开服务器地址，请检查是否能正常访问', 'testing');
        }

        // CORS测试
        async function testWithCORS() {
            const address = document.getElementById('serverAddress').value.trim();
            if (!address) {
                updateConnectionStatus('请输入服务器地址', 'error');
                return;
            }

            updateConnectionStatus('正在测试CORS设置...', 'testing');
            addDebugLog(`CORS测试: ${address}`);

            try {
                // 尝试带CORS的请求
                const response = await fetch(address + '/screenshot', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Access-Control-Allow-Origin': '*'
                    },
                    mode: 'cors',
                    body: JSON.stringify({}),
                    signal: AbortSignal.timeout(8000)
                });

                addDebugLog(`CORS测试响应: ${response.status}`);

                if (response.ok) {
                    updateConnectionStatus('✅ CORS测试成功！', 'success');
                    return true;
                } else {
                    updateConnectionStatus(`⚠️ CORS测试响应: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                addDebugLog(`CORS测试错误: ${error.message}`);
                updateConnectionStatus('❌ CORS测试失败: ' + error.message, 'error');
                return false;
            }
        }

        // 设置状态
        function setStatus(message, isLoading = false) {
            const statusBar = document.getElementById('statusBar');
            statusBar.innerHTML = isLoading ? `<span class="loading"></span> ${message}` : message;

            // 同时添加到调试日志
            addDebugLog(message);
        }

        // 添加调试日志
        function addDebugLog(message) {
            const debugLog = document.getElementById('debugLog');
            const time = new Date().toLocaleTimeString();
            debugLog.innerHTML = `[${time}] ${message}<br>` + debugLog.innerHTML;

            // 限制日志条数
            const lines = debugLog.innerHTML.split('<br>');
            if (lines.length > 10) {
                debugLog.innerHTML = lines.slice(0, 10).join('<br>');
            }
        }

        // 截图功能
        async function captureScreen() {
            if (!API_BASE) {
                setStatus('请先配置服务器地址');
                showServerConfig();
                return;
            }

            setStatus('正在截图...', true);
            try {
                const response = await fetch(API_BASE + '/screenshot', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                if (result.success) {
                    const img = document.getElementById('deviceScreen');
                    img.src = `${API_BASE}/screenshots/${result.filename}?t=${Date.now()}`;
                    img.onload = function() {
                        calculateScreenScale();
                        setStatus('截图完成');
                    };
                } else {
                    setStatus('截图失败: ' + result.error);
                }
            } catch (error) {
                setStatus('截图失败: ' + error.message);
            }
        }

        // 计算屏幕缩放比例
        function calculateScreenScale() {
            const img = document.getElementById('deviceScreen');
            if (!img.naturalWidth || !img.naturalHeight) {
                screenScale = 1;
                return;
            }

            // 计算图片在容器中的实际显示尺寸
            const container = img.parentElement;
            const containerAspect = container.clientWidth / container.clientHeight;
            const imageAspect = img.naturalWidth / img.naturalHeight;

            if (imageAspect > containerAspect) {
                // 图片更宽，以宽度为准
                screenScale = container.clientWidth / img.naturalWidth;
            } else {
                // 图片更高，以高度为准
                screenScale = container.clientHeight / img.naturalHeight;
            }

            console.log('屏幕缩放比例:', screenScale, '原始尺寸:', img.naturalWidth, 'x', img.naturalHeight);
        }

        // 分析UI元素
        async function analyzeUI() {
            if (!API_BASE) {
                setStatus('请先配置服务器地址');
                showServerConfig();
                return;
            }

            setStatus('正在分析UI元素...', true);
            try {
                const response = await fetch(API_BASE + '/ui/dump', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                console.log('UI分析结果:', result);

                if (result.success) {
                    // 后端返回的是XML字符串，需要解析
                    const xmlContent = result.result || '';
                    uiElements = parseUIXML(xmlContent);

                    // 切换到UI显示模式
                    currentDisplayMode = 'ui';
                    renderUIElements();
                    renderOverlayElements();
                    setStatus(`发现 ${uiElements.length} 个UI元素`);
                } else {
                    setStatus('UI分析失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('UI分析错误:', error);
                setStatus('UI分析失败: ' + error.message);
            }
        }

        // 分析OCR文字
        async function analyzeOCR() {
            if (!API_BASE) {
                setStatus('请先配置服务器地址');
                showServerConfig();
                return;
            }

            setStatus('正在分析OCR文字...', true);
            try {
                const response = await fetch(API_BASE + '/ocr', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                console.log('OCR分析结果:', result);

                if (result.success) {
                    // 后端返回的是texts数组，直接使用
                    ocrTexts = result.texts || [];
                    console.log('OCR解析结果:', ocrTexts);

                    // 切换到OCR显示模式
                    currentDisplayMode = 'ocr';
                    renderOCRTexts();
                    renderOverlayElements();
                    setStatus(`发现 ${ocrTexts.length} 个文字区域`);
                } else if (result.message) {
                    // 处理未识别到文字的情况
                    ocrTexts = [];
                    currentDisplayMode = 'ocr';
                    renderOCRTexts();
                    renderOverlayElements();
                    setStatus('未识别到文字');
                } else {
                    setStatus('OCR分析失败: ' + (result.error || '未知错误'));
                }
            } catch (error) {
                console.error('OCR分析错误:', error);
                setStatus('OCR分析失败: ' + error.message);
            }
        }

        // 解析UI XML
        function parseUIXML(xmlContent) {
            const elements = [];

            try {
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(xmlContent, 'text/xml');
                const nodes = xmlDoc.querySelectorAll('node');

                nodes.forEach(node => {
                    const bounds = node.getAttribute('bounds');
                    const text = node.getAttribute('text');
                    const className = node.getAttribute('class');
                    const resourceId = node.getAttribute('resource-id');
                    const clickable = node.getAttribute('clickable') === 'true';

                    if (bounds) {  // 显示所有有bounds的元素，不只是可点击的
                        // 解析bounds: [left,top][right,bottom]
                        const boundsMatch = bounds.match(/\[(\d+),(\d+)\]\[(\d+),(\d+)\]/);
                        if (boundsMatch) {
                            const left = parseInt(boundsMatch[1]);
                            const top = parseInt(boundsMatch[2]);
                            const right = parseInt(boundsMatch[3]);
                            const bottom = parseInt(boundsMatch[4]);

                            const element = {
                                text: text || '',
                                className: className || '',
                                resourceId: resourceId || '',
                                clickable: clickable,
                                bounds: {
                                    x: left,
                                    y: top,
                                    width: right - left,
                                    height: bottom - top,
                                    left: left,
                                    top: top,
                                    right: right,
                                    bottom: bottom
                                }
                            };
                            elements.push(element);
                        }
                    }
                });
            } catch (error) {
                console.error('解析UI XML失败:', error);
            }

            return elements;
        }



        // 统一的覆盖层渲染函数
        function renderOverlayElements() {
            const overlay = document.getElementById('overlayElements');

            // 清空所有覆盖层元素
            overlay.innerHTML = '';

            if (currentDisplayMode === 'ui') {
                renderUIOverlay();
            } else if (currentDisplayMode === 'ocr') {
                renderOCROverlay();
            }
        }

        // 渲染UI覆盖层
        function renderUIOverlay() {
            const overlay = document.getElementById('overlayElements');
            const img = document.getElementById('deviceScreen');

            if (!img.naturalWidth || !img.naturalHeight) return;

            // 计算实际图片在容器中的位置和尺寸（考虑object-fit: contain）
            const containerWidth = img.clientWidth;
            const containerHeight = img.clientHeight;
            const imageAspect = img.naturalWidth / img.naturalHeight;
            const containerAspect = containerWidth / containerHeight;

            let actualWidth, actualHeight, offsetX, offsetY;

            if (imageAspect > containerAspect) {
                // 图片更宽，以宽度为准
                actualWidth = containerWidth;
                actualHeight = containerWidth / imageAspect;
                offsetX = 0;
                offsetY = (containerHeight - actualHeight) / 2;
            } else {
                // 图片更高，以高度为准
                actualHeight = containerHeight;
                actualWidth = containerHeight * imageAspect;
                offsetX = (containerWidth - actualWidth) / 2;
                offsetY = 0;
            }

            const scaleX = actualWidth / img.naturalWidth;
            const scaleY = actualHeight / img.naturalHeight;

            console.log('UI覆盖层渲染 - 图片尺寸:', img.naturalWidth, 'x', img.naturalHeight, '显示尺寸:', img.clientWidth, 'x', img.clientHeight, '缩放:', scaleX, scaleY);

            uiElements.forEach((element, index) => {
                // 检查bounds是否存在和合理
                if (!element.bounds || typeof element.bounds.x === 'undefined') {
                    console.warn('UI元素缺少bounds信息:', element);
                    return;
                }

                // 检查尺寸是否合理
                if (element.bounds.width > img.naturalWidth || element.bounds.height > img.naturalHeight) {
                    console.warn('UI元素尺寸异常:', element.bounds, '图片尺寸:', img.naturalWidth, 'x', img.naturalHeight);
                    return;
                }

                // 过滤掉太小的元素（参考Python脚本的逻辑）
                if (element.bounds.width < 10 || element.bounds.height < 10) {
                    console.log('跳过太小的UI元素:', element.bounds.width, 'x', element.bounds.height);
                    return;
                }

                const overlayEl = document.createElement('div');

                // 根据元素类型设置样式类
                let elementClass = 'ui-element ';
                const className = element.className || '';

                if (className.includes('Button')) {
                    elementClass += 'button';
                } else if (className.includes('EditText') || className.includes('TextField')) {
                    elementClass += 'edittext';
                } else if (className.includes('TextView') || className.includes('Text')) {
                    elementClass += 'textview';
                } else if (className.includes('Image')) {
                    elementClass += 'image';
                } else if (className.includes('Layout') || className.includes('View')) {
                    elementClass += 'layout';
                } else if (element.clickable) {
                    elementClass += 'clickable';
                } else {
                    elementClass += 'default';
                }

                overlayEl.className = elementClass;

                // 精确计算位置和尺寸（加上偏移量）
                let left = Math.round(element.bounds.x * scaleX + offsetX);
                let top = Math.round(element.bounds.y * scaleY + offsetY);
                let width = Math.round(element.bounds.width * scaleX);
                let height = Math.round(element.bounds.height * scaleY);

                // 确保边框完全在实际图片范围内
                const imgLeft = offsetX;
                const imgTop = offsetY;
                const imgRight = offsetX + actualWidth;
                const imgBottom = offsetY + actualHeight;

                if (left < imgLeft) {
                    width -= (imgLeft - left);
                    left = imgLeft;
                }
                if (top < imgTop) {
                    height -= (imgTop - top);
                    top = imgTop;
                }
                if (left + width > imgRight) {
                    width = imgRight - left;
                }
                if (top + height > imgBottom) {
                    height = imgBottom - top;
                }

                // 跳过太小的元素
                if (width < 2 || height < 2) {
                    console.log(`跳过太小的UI元素: ${width}x${height}`);
                    return;
                }

                overlayEl.style.left = left + 'px';
                overlayEl.style.top = top + 'px';
                overlayEl.style.width = width + 'px';
                overlayEl.style.height = height + 'px';

                // 设置工具提示
                const tooltip = [
                    `类型: ${className}`,
                    element.text ? `文本: ${element.text}` : '',
                    element.resourceId ? `ID: ${element.resourceId}` : '',
                    `位置: (${element.bounds.x}, ${element.bounds.y})`,
                    `尺寸: ${element.bounds.width}x${element.bounds.height}`,
                    element.clickable ? '可点击' : ''
                ].filter(Boolean).join('\n');

                overlayEl.title = tooltip;
                overlayEl.onclick = (e) => {
                    e.stopPropagation();
                    selectUIElement(index);
                };

                // 如果是可点击元素，添加红色外框
                if (element.clickable) {
                    overlayEl.style.boxShadow = 'inset 0 0 0 1px red';
                }

                overlay.appendChild(overlayEl);
            });
        }

        // 渲染OCR覆盖层
        function renderOCROverlay() {
            const overlay = document.getElementById('overlayElements');
            const img = document.getElementById('deviceScreen');

            if (!img.naturalWidth || !img.naturalHeight) return;

            // 计算实际图片在容器中的位置和尺寸（考虑object-fit: contain）
            const containerWidth = img.clientWidth;
            const containerHeight = img.clientHeight;
            const imageAspect = img.naturalWidth / img.naturalHeight;
            const containerAspect = containerWidth / containerHeight;

            let actualWidth, actualHeight, offsetX, offsetY;

            if (imageAspect > containerAspect) {
                // 图片更宽，以宽度为准
                actualWidth = containerWidth;
                actualHeight = containerWidth / imageAspect;
                offsetX = 0;
                offsetY = (containerHeight - actualHeight) / 2;
            } else {
                // 图片更高，以高度为准
                actualHeight = containerHeight;
                actualWidth = containerHeight * imageAspect;
                offsetX = (containerWidth - actualWidth) / 2;
                offsetY = 0;
            }

            const scaleX = actualWidth / img.naturalWidth;
            const scaleY = actualHeight / img.naturalHeight;

            ocrTexts.forEach((text, index) => {
                // 检查bounds是否存在
                if (!text.bounds || typeof text.bounds.x === 'undefined') {
                    console.warn('OCR文字缺少bounds信息:', text);
                    return;
                }

                const overlayEl = document.createElement('div');
                overlayEl.className = 'ocr-text';
                overlayEl.style.left = (text.bounds.x * scaleX + offsetX) + 'px';
                overlayEl.style.top = (text.bounds.y * scaleY + offsetY) + 'px';
                overlayEl.style.width = (text.bounds.width * scaleX) + 'px';
                overlayEl.style.height = (text.bounds.height * scaleY) + 'px';
                overlayEl.title = `OCR文字: ${text.text}`;
                overlayEl.textContent = text.text; // 显示文字内容
                overlayEl.onclick = (e) => {
                    e.stopPropagation();
                    selectOCRText(index);
                };
                overlay.appendChild(overlayEl);
            });
        }

        // 渲染UI元素列表
        function renderUIElements() {
            const container = document.getElementById('uiElements');

            // 清空现有元素
            container.innerHTML = '';

            uiElements.forEach((element, index) => {
                // 创建列表项
                const item = document.createElement('div');
                item.className = 'action-item';
                // 显示指纹信息而不是常规属性
                const displayName = element.text || element.className || element.resourceId || '未知元素';

                // 解析指纹信息
                let fingerprintInfo = `类名: ${element.className || '无'}`;
                if (element.fingerprint) {
                    try {
                        const fp = typeof element.fingerprint === 'string' ?
                            JSON.parse(element.fingerprint) : element.fingerprint;

                        // 检查是否是增强指纹
                        if (fp.primary && fp.metadata) {
                            const confidence = Math.round((fp.metadata.confidence || 0) * 100);
                            const region = fp.primary.screen_region || 'unknown';
                            fingerprintInfo = `增强指纹: ${region} (置信度: ${confidence}%)`;
                        } else {
                            fingerprintInfo = `指纹: ${fp.hash || 'unknown'} (分数: ${fp.score || 0})`;
                        }
                    } catch (e) {
                        fingerprintInfo = `指纹: ${element.fingerprint.substring(0, 20)}...`;
                    }
                }

                item.innerHTML = `
                    <div class="action-header">
                        <span class="action-type">UI</span>
                        <span class="action-name">${displayName}</span>
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        ${fingerprintInfo}<br>
                        位置: (${element.bounds.x}, ${element.bounds.y})
                        大小: ${element.bounds.width}x${element.bounds.height}
                    </div>
                `;
                item.onclick = () => selectUIElement(index);
                container.appendChild(item);
            });
        }

        // 渲染OCR文字列表
        function renderOCRTexts() {
            const container = document.getElementById('ocrTexts');

            // 清空现有元素
            container.innerHTML = '';

            ocrTexts.forEach((text, index) => {
                // 创建列表项
                const item = document.createElement('div');
                item.className = 'action-item';

                // 检查bounds是否存在
                const boundsInfo = text.bounds ?
                    `位置: (${text.bounds.x}, ${text.bounds.y}) 大小: ${text.bounds.width}x${text.bounds.height}` :
                    '位置信息缺失';

                item.innerHTML = `
                    <div class="action-header">
                        <span class="action-type">OCR</span>
                        <span class="action-name">${text.text}</span>
                    </div>
                    <div style="font-size: 12px; color: #666;">
                        ${boundsInfo}<br>
                        置信度: ${(text.confidence * 100).toFixed(1)}%
                    </div>
                `;
                item.onclick = () => selectOCRText(index);
                container.appendChild(item);
            });
        }

        // 选择UI元素
        function selectUIElement(index) {
            selectedElement = { type: 'ui', index: index, data: uiElements[index] };
            updateSelectedElement();

            // 更新视觉选择状态
            document.querySelectorAll('.ui-element, .action-item').forEach(el => el.classList.remove('selected'));
            document.querySelectorAll('.ui-element')[index]?.classList.add('selected');
            document.querySelectorAll('#uiElements .action-item')[index]?.classList.add('selected');
        }

        // 选择OCR文字
        function selectOCRText(index) {
            selectedElement = { type: 'ocr', index: index, data: ocrTexts[index] };
            updateSelectedElement();

            // 更新视觉选择状态
            document.querySelectorAll('.ocr-text, .action-item').forEach(el => el.classList.remove('selected'));
            document.querySelectorAll('.ocr-text')[index]?.classList.add('selected');
            document.querySelectorAll('#ocrTexts .action-item')[index]?.classList.add('selected');
        }

        // 更新选中元素信息
        function updateSelectedElement() {
            const infoPanel = document.getElementById('selectedElementInfo');
            const detailsDiv = document.getElementById('elementDetails');

            if (!selectedElement) {
                infoPanel.style.display = 'none';
                return;
            }

            infoPanel.style.display = 'block';
            const data = selectedElement.data;

            if (selectedElement.type === 'ui') {
                // 解析指纹信息
                let fingerprintDisplay = '';
                if (data.fingerprint) {
                    try {
                        const fp = typeof data.fingerprint === 'string' ?
                            JSON.parse(data.fingerprint) : data.fingerprint;

                        // 检查是否是增强指纹
                        if (fp.primary && fp.metadata) {
                            const confidence = Math.round((fp.metadata.confidence || 0) * 100);
                            fingerprintDisplay = `
                                <div class="element-property">
                                    <span class="key">指纹类型:</span>
                                    <span class="value">增强指纹</span>
                                </div>
                                <div class="element-property">
                                    <span class="key">屏幕区域:</span>
                                    <span class="value">${fp.primary.screen_region || 'unknown'}</span>
                                </div>
                                <div class="element-property">
                                    <span class="key">置信度:</span>
                                    <span class="value">${confidence}%</span>
                                </div>
                                <div class="element-property">
                                    <span class="key">创建时间:</span>
                                    <span class="value">${fp.metadata.created_at || 'unknown'}</span>
                                </div>
                            `;
                        } else {
                            fingerprintDisplay = `
                                <div class="element-property">
                                    <span class="key">指纹哈希:</span>
                                    <span class="value">${fp.hash || 'unknown'}</span>
                                </div>
                                <div class="element-property">
                                    <span class="key">匹配分数:</span>
                                    <span class="value">${fp.score || 0}</span>
                                </div>
                            `;
                        }
                    } catch (e) {
                        fingerprintDisplay = `
                            <div class="element-property">
                                <span class="key">指纹:</span>
                                <span class="value">${data.fingerprint.substring(0, 30)}...</span>
                            </div>
                        `;
                    }
                }

                detailsDiv.innerHTML = `
                    <div class="element-property">
                        <span class="key">类型:</span>
                        <span class="value">UI元素</span>
                    </div>
                    <div class="element-property">
                        <span class="key">文本:</span>
                        <span class="value">${data.text || '无'}</span>
                    </div>
                    ${fingerprintDisplay}
                    <div class="element-property">
                        <span class="key">类名:</span>
                        <span class="value">${data.className || '无'}</span>
                    </div>
                    <div class="element-property">
                        <span class="key">资源ID:</span>
                        <span class="value">${data.resourceId || '无'}</span>
                    </div>
                    <div class="element-property">
                        <span class="key">位置:</span>
                        <span class="value">(${data.bounds.x}, ${data.bounds.y})</span>
                    </div>
                    <div class="element-property">
                        <span class="key">大小:</span>
                        <span class="value">${data.bounds.width} x ${data.bounds.height}</span>
                    </div>
                `;
            } else if (selectedElement.type === 'ocr') {
                detailsDiv.innerHTML = `
                    <div class="element-property">
                        <span class="key">类型:</span>
                        <span class="value">OCR文字</span>
                    </div>
                    <div class="element-property">
                        <span class="key">文本:</span>
                        <span class="value">${data.text}</span>
                    </div>
                    <div class="element-property">
                        <span class="key">位置:</span>
                        <span class="value">(${data.x}, ${data.y})</span>
                    </div>
                    <div class="element-property">
                        <span class="key">大小:</span>
                        <span class="value">${data.width} x ${data.height}</span>
                    </div>
                    <div class="element-property">
                        <span class="key">置信度:</span>
                        <span class="value">${(data.confidence * 100).toFixed(1)}%</span>
                    </div>
                `;
            }
        }

        // 从选中元素创建动作
        function createActionFromElement() {
            if (!selectedElement) {
                setStatus('请先选择一个元素');
                return;
            }

            const data = selectedElement.data;

            if (selectedElement.type === 'ui') {
                document.getElementById('actionType').value = 'ui_click';

                // 生成包含指纹信息的动作名称
                let actionName = `点击 ${data.text || data.className || 'UI元素'}`;
                if (data.fingerprint) {
                    try {
                        const fp = typeof data.fingerprint === 'string' ?
                            JSON.parse(data.fingerprint) : data.fingerprint;

                        // 检查是否是增强指纹
                        if (fp.primary && fp.metadata) {
                            const region = fp.primary.screen_region || 'unknown';
                            const elementDesc = data.text || data.className || 'UI元素';
                            actionName = `点击增强指纹元素 [${region}] (${elementDesc})`;
                        } else {
                            actionName = `点击指纹元素 ${fp.hash || 'unknown'} (${data.text || data.className || 'UI元素'})`;
                        }
                    } catch (e) {
                        actionName = `点击指纹元素 (${data.text || data.className || 'UI元素'})`;
                    }
                }
                document.getElementById('actionName').value = actionName;
            } else if (selectedElement.type === 'ocr') {
                document.getElementById('actionType').value = 'ocr_click';
                document.getElementById('actionName').value = `点击文字 "${data.text}"`;
            }

            updateActionForm();
            setStatus('已自动填充动作信息');
        }

        // 屏幕点击事件
        async function onScreenClick(event) {
            const img = event.target;
            const rect = img.getBoundingClientRect();

            // 计算图片的实际显示尺寸和位置（考虑object-fit: contain）
            const containerWidth = img.clientWidth;
            const containerHeight = img.clientHeight;
            const naturalWidth = img.naturalWidth;
            const naturalHeight = img.naturalHeight;
            const imageAspect = naturalWidth / naturalHeight;
            const containerAspect = containerWidth / containerHeight;

            let actualWidth, actualHeight, offsetX, offsetY;

            if (imageAspect > containerAspect) {
                // 图片更宽，以宽度为准
                actualWidth = containerWidth;
                actualHeight = containerWidth / imageAspect;
                offsetX = 0;
                offsetY = (containerHeight - actualHeight) / 2;
            } else {
                // 图片更高，以高度为准
                actualHeight = containerHeight;
                actualWidth = containerHeight * imageAspect;
                offsetX = (containerWidth - actualWidth) / 2;
                offsetY = 0;
            }

            // 计算点击位置相对于容器的坐标
            const clickX = event.clientX - rect.left;
            const clickY = event.clientY - rect.top;

            // 检查点击是否在实际图片区域内
            if (clickX < offsetX || clickX > offsetX + actualWidth ||
                clickY < offsetY || clickY > offsetY + actualHeight) {
                setStatus('请点击图片区域');
                return;
            }

            // 转换为相对于实际图片的坐标
            const relativeX = clickX - offsetX;
            const relativeY = clickY - offsetY;

            // 转换为原始图片坐标
            const x = Math.round((relativeX / actualWidth) * naturalWidth);
            const y = Math.round((relativeY / actualHeight) * naturalHeight);



            setStatus(`点击坐标: (${x}, ${y})，正在获取UI指纹...`, true);

            try {
                // 获取点击位置的UI指纹
                const fingerprint = await getUIFingerprintAtPosition(x, y);

                if (fingerprint) {
                    // 找到UI元素，创建UI点击动作
                    document.getElementById('actionType').value = 'ui_click';
                    // 生成包含指纹信息的动作名称
                    let actionName = `UI点击: ${fingerprint.text || fingerprint.className || '未知元素'}`;
                    if (fingerprint.fingerprint) {
                        try {
                            const fp = typeof fingerprint.fingerprint === 'string' ?
                                JSON.parse(fingerprint.fingerprint) : fingerprint.fingerprint;

                            // 检查是否是增强指纹
                            if (fp.primary && fp.metadata) {
                                const region = fp.primary.screen_region || 'unknown';
                                const elementDesc = fingerprint.text || fingerprint.className || '未知元素';
                                actionName = `UI增强指纹点击: [${region}] (${elementDesc})`;
                            } else {
                                actionName = `UI指纹点击: ${fp.hash || 'unknown'} (${fingerprint.text || fingerprint.className || '未知元素'})`;
                            }
                        } catch (e) {
                            actionName = `UI指纹点击: (${fingerprint.text || fingerprint.className || '未知元素'})`;
                        }
                    }
                    document.getElementById('actionName').value = actionName;

                    // 设置选中的UI元素
                    selectedElement = {
                        type: 'ui',
                        data: fingerprint,
                        position: { x, y }
                    };

                    updateActionForm();
                    updateSelectedElement();
                    setStatus(`已选择UI元素: ${fingerprint.text || fingerprint.className || '未知元素'}`);

                    // 高亮显示选中的元素
                    highlightSelectedElement(fingerprint.bounds);
                } else {
                    // 没有UI元素，创建普通点击动作
                    document.getElementById('actionType').value = 'click';
                    document.getElementById('actionName').value = `点击坐标 (${x}, ${y})`;
                    updateActionForm();

                    // 填充坐标
                    setTimeout(() => {
                        const xInput = document.getElementById('clickX');
                        const yInput = document.getElementById('clickY');
                        if (xInput) xInput.value = x;
                        if (yInput) yInput.value = y;
                    }, 100);

                    setStatus(`该位置无UI元素，已设置为普通点击: (${x}, ${y})`);
                }
            } catch (error) {
                console.error('获取UI指纹失败:', error);
                // 出错时回退到普通点击
                document.getElementById('actionType').value = 'click';
                document.getElementById('actionName').value = `点击坐标 (${x}, ${y})`;
                updateActionForm();

                setTimeout(() => {
                    const xInput = document.getElementById('clickX');
                    const yInput = document.getElementById('clickY');
                    if (xInput) xInput.value = x;
                    if (yInput) yInput.value = y;
                }, 100);

                setStatus(`获取UI指纹失败，已设置为普通点击: (${x}, ${y})`);
            }
        }

        // 获取指定位置的UI指纹
        async function getUIFingerprintAtPosition(x, y) {
            if (!API_BASE) {
                throw new Error('请先配置服务器地址');
            }

            try {
                // 调用后端API生成指纹
                const response = await fetch(API_BASE + '/ui/fingerprint', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ x: x, y: y })
                });

                const result = await response.json();
                console.log('指纹生成结果:', result);

                if (result.success && result.fingerprint) {
                    let fingerprint;

                    // 处理不同格式的指纹数据
                    if (typeof result.fingerprint === 'object') {
                        fingerprint = result.fingerprint;
                    } else {
                        try {
                            fingerprint = JSON.parse(result.fingerprint);
                        } catch (e) {
                            console.warn('指纹不是有效的JSON:', result.fingerprint);
                            return null;
                        }
                    }

                    // 获取屏幕尺寸
                    const img = document.getElementById('deviceScreen');
                    const screenWidth = img.naturalWidth || 1080;
                    const screenHeight = img.naturalHeight || 1920;

                    // 转换为前端需要的格式
                    let element;

                    // 检查是否是增强指纹格式
                    if (fingerprint.primary && fingerprint.metadata) {
                        // 增强指纹格式
                        const screenWidth = fingerprint.metadata.screen_width || img.naturalWidth || 1080;
                        const screenHeight = fingerprint.metadata.screen_height || img.naturalHeight || 1920;

                        // 从增强指纹中提取信息
                        const posRatio = fingerprint.primary.position_ratio || {};
                        const sizeRange = fingerprint.primary.size_range || {};

                        // 计算位置和尺寸
                        const centerX = Math.round((posRatio.x || 0) * screenWidth);
                        const centerY = Math.round((posRatio.y || 0) * screenHeight);
                        const width = Math.round((sizeRange.min_width + sizeRange.max_width) / 2) || 50;
                        const height = Math.round((sizeRange.min_height + sizeRange.max_height) / 2) || 30;
                        const x = centerX - width / 2;
                        const y = centerY - height / 2;

                        element = {
                            text: fingerprint.secondary?.text_pattern || '',
                            className: fingerprint.primary?.class || '',
                            resourceId: fingerprint.secondary?.resource_id_pattern || '',
                            description: fingerprint.metadata?.description || '',
                            clickable: fingerprint.primary?.clickable || true,
                            fingerprint: JSON.stringify(fingerprint), // 保存原始指纹JSON字符串
                            bounds: {
                                x: x, y: y, width: width, height: height,
                                left: x, top: y, right: x + width, bottom: y + height
                            }
                        };
                    } else {
                        // 旧格式或简单格式
                        element = {
                            text: fingerprint.text || '',
                            className: fingerprint.class || '',
                            resourceId: fingerprint.id || '',
                            description: fingerprint.desc || '',
                            clickable: true,
                            fingerprint: JSON.stringify(fingerprint), // 保存原始指纹JSON字符串
                            bounds: fingerprint.pos ? {
                                x: Math.round(fingerprint.pos.x * screenWidth),
                                y: Math.round(fingerprint.pos.y * screenHeight),
                                width: Math.round(fingerprint.pos.w * screenWidth),
                                height: Math.round(fingerprint.pos.h * screenHeight),
                                left: Math.round(fingerprint.pos.x * screenWidth),
                                top: Math.round(fingerprint.pos.y * screenHeight),
                                right: Math.round((fingerprint.pos.x + fingerprint.pos.w) * screenWidth),
                                bottom: Math.round((fingerprint.pos.y + fingerprint.pos.h) * screenHeight)
                            } : {
                                x: x, y: y, width: 50, height: 30,
                                left: x, top: y, right: x + 50, bottom: y + 30
                            }
                        };
                    }

                    return element;
                }

                return null; // 没有找到UI元素
            } catch (error) {
                console.error('获取UI指纹失败:', error);
                throw error;
            }
        }

        // 高亮显示选中的元素
        function highlightSelectedElement(bounds) {
            // 移除之前的高亮
            const overlay = document.getElementById('overlayElements');
            overlay.querySelectorAll('.selected-element').forEach(el => el.remove());

            // 添加新的高亮框
            const highlight = document.createElement('div');
            highlight.className = 'selected-element';
            highlight.style.position = 'absolute';
            highlight.style.left = (bounds.left * screenScale) + 'px';
            highlight.style.top = (bounds.top * screenScale) + 'px';
            highlight.style.width = (bounds.width * screenScale) + 'px';
            highlight.style.height = (bounds.height * screenScale) + 'px';
            highlight.style.border = '3px solid #ff0000';
            highlight.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
            highlight.style.pointerEvents = 'none';
            highlight.style.zIndex = '1000';
            highlight.style.boxSizing = 'border-box';

            overlay.appendChild(highlight);
        }

        // 切换标签页
        function switchTab(tab) {
            currentTab = tab;

            // 更新标签按钮状态
            document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`.tab:nth-child(${tab === 'ui' ? 1 : 2})`).classList.add('active');

            // 更新内容显示
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));
            document.getElementById(tab + 'Tab').classList.add('active');

            // 清除选择状态
            selectedElement = null;
            updateSelectedElement();
            document.querySelectorAll('.ui-element, .ocr-text, .action-item').forEach(el => el.classList.remove('selected'));
        }

        // 切换流程类型
        function switchFlow(flow) {
            currentFlow = flow;

            // 更新标签按钮状态
            document.querySelectorAll('.flow-tab').forEach(t => t.classList.remove('active'));
            document.querySelector(`.flow-tab:nth-child(${flow === 'sequential' ? 1 : flow === 'parallel' ? 2 : 3})`).classList.add('active');

            // 更新动作列表
            renderActionList();
        }

        // 更新动作表单
        function updateActionForm() {
            const actionType = document.getElementById('actionType').value;
            const formDiv = document.getElementById('actionForm');

            let formHTML = '';

            switch (actionType) {
                case 'click':
                    formHTML = `
                        <div class="form-group">
                            <label>X坐标:</label>
                            <input type="number" class="form-control" id="clickX" placeholder="X坐标">
                        </div>
                        <div class="form-group">
                            <label>Y坐标:</label>
                            <input type="number" class="form-control" id="clickY" placeholder="Y坐标">
                        </div>
                    `;
                    break;

                case 'ocr_click':
                    const ocrText = selectedElement?.type === 'ocr' ? selectedElement.data.text : '';
                    formHTML = `
                        <div class="form-group">
                            <label>要点击的文字:</label>
                            <input type="text" class="form-control" id="ocrText" placeholder="输入要点击的文字" value="${ocrText}">
                        </div>
                    `;
                    break;

                case 'ui_click':
                    const currentFingerprint = selectedElement?.type === 'ui' ? selectedElement.data.fingerprint || '' : '';
                    formHTML = `
                        <div class="form-group">
                            <label>UI指纹:</label>
                            <textarea class="form-control" id="uiFingerprint" rows="4" placeholder="输入UI元素指纹JSON，格式如：{&quot;hash&quot;:&quot;c84e6d9e&quot;,&quot;score&quot;:55,&quot;id&quot;:&quot;com.example:id/button&quot;,&quot;class&quot;:&quot;android.widget.Button&quot;,&quot;pos&quot;:{&quot;x&quot;:0.5,&quot;y&quot;:0.6,&quot;w&quot;:0.3,&quot;h&quot;:0.1}}">${currentFingerprint}</textarea>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                ${selectedElement?.type === 'ui' ? '已从选中元素自动填充指纹' : '可以点击截图上的UI元素自动获取指纹，或手动输入指纹JSON'}
                            </div>
                        </div>
                        <div class="form-group">
                            <label>备用坐标 (可选):</label>
                            <div style="display: flex; gap: 10px;">
                                <input type="number" class="form-control" id="uiBackupX" placeholder="X坐标" style="flex: 1;">
                                <input type="number" class="form-control" id="uiBackupY" placeholder="Y坐标" style="flex: 1;">
                            </div>
                            <div style="font-size: 12px; color: #666; margin-top: 5px;">
                                当指纹匹配失败时使用的备用坐标
                            </div>
                        </div>
                    `;
                    break;

                case 'swipe':
                    formHTML = `
                        <div class="form-group">
                            <label>起始X坐标:</label>
                            <input type="number" class="form-control" id="swipeX1" placeholder="起始X坐标">
                        </div>
                        <div class="form-group">
                            <label>起始Y坐标:</label>
                            <input type="number" class="form-control" id="swipeY1" placeholder="起始Y坐标">
                        </div>
                        <div class="form-group">
                            <label>结束X坐标:</label>
                            <input type="number" class="form-control" id="swipeX2" placeholder="结束X坐标">
                        </div>
                        <div class="form-group">
                            <label>结束Y坐标:</label>
                            <input type="number" class="form-control" id="swipeY2" placeholder="结束Y坐标">
                        </div>
                        <div class="form-group">
                            <label>持续时间(毫秒):</label>
                            <input type="number" class="form-control" id="swipeDuration" placeholder="500" value="500">
                        </div>
                    `;
                    break;

                case 'input':
                    formHTML = `
                        <div class="form-group">
                            <label>输入文字:</label>
                            <input type="text" class="form-control" id="inputText" placeholder="要输入的文字">
                        </div>
                    `;
                    break;

                case 'wait':
                    formHTML = `
                        <div class="form-group">
                            <label>等待时间(毫秒):</label>
                            <input type="number" class="form-control" id="waitDuration" placeholder="1000" value="1000">
                        </div>
                    `;
                    break;

                case 'app_launch':
                    formHTML = `
                        <div class="form-group">
                            <label>应用包名:</label>
                            <input type="text" class="form-control" id="appPackage" placeholder="com.example.app">
                        </div>
                    `;
                    break;

                case 'multi_target':
                    formHTML = `
                        <div class="form-group">
                            <label>多条件匹配:</label>
                            <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                                当多个条件同时出现时，点击指定的目标
                            </div>
                            <button type="button" class="btn btn-small" onclick="addMultiTarget()">➕ 添加条件</button>
                            <div id="multiTargets"></div>
                        </div>
                        <div class="form-group">
                            <label>目标索引:</label>
                            <input type="number" class="form-control" id="targetIndex" placeholder="0" value="0">
                            <div style="font-size: 12px; color: #666;">选择要点击的目标序号(从0开始)</div>
                        </div>
                    `;
                    break;
            }

            formDiv.innerHTML = formHTML;
        }

        // 添加动作
        function addAction() {
            const actionType = document.getElementById('actionType').value;
            const actionName = document.getElementById('actionName').value;

            if (!actionName.trim()) {
                setStatus('请输入动作名称');
                return;
            }

            const action = {
                t: actionType,
                n: actionName,
                e: true,
                p: {}
            };

            // 根据动作类型收集参数
            switch (actionType) {
                case 'click':
                    const x = parseInt(document.getElementById('clickX').value);
                    const y = parseInt(document.getElementById('clickY').value);
                    if (isNaN(x) || isNaN(y)) {
                        setStatus('请输入有效的坐标');
                        return;
                    }
                    action.p = { x, y };
                    break;

                case 'ocr_click':
                    const text = document.getElementById('ocrText').value.trim();
                    if (!text) {
                        setStatus('请输入要点击的文字');
                        return;
                    }
                    action.p = { text };
                    break;

                case 'ui_click':
                    const fingerprintText = document.getElementById('uiFingerprint').value.trim();
                    if (!fingerprintText) {
                        setStatus('请输入UI指纹或选择UI元素');
                        return;
                    }

                    // 验证指纹格式
                    try {
                        const fingerprintObj = JSON.parse(fingerprintText);
                        if (!fingerprintObj.hash) {
                            setStatus('指纹格式错误：缺少hash字段');
                            return;
                        }
                    } catch (e) {
                        setStatus('指纹格式错误：不是有效的JSON');
                        return;
                    }

                    action.p = {
                        selector: {
                            fingerprint: fingerprintText
                        }
                    };

                    // 添加备用坐标（如果有）
                    const backupX = document.getElementById('uiBackupX').value;
                    const backupY = document.getElementById('uiBackupY').value;
                    if (backupX && backupY) {
                        action.p.backup_coords = {
                            x: parseInt(backupX),
                            y: parseInt(backupY)
                        };
                    }
                    break;

                case 'swipe':
                    const x1 = parseInt(document.getElementById('swipeX1').value);
                    const y1 = parseInt(document.getElementById('swipeY1').value);
                    const x2 = parseInt(document.getElementById('swipeX2').value);
                    const y2 = parseInt(document.getElementById('swipeY2').value);
                    const duration = parseInt(document.getElementById('swipeDuration').value) || 500;
                    if (isNaN(x1) || isNaN(y1) || isNaN(x2) || isNaN(y2)) {
                        setStatus('请输入有效的滑动坐标');
                        return;
                    }
                    action.p = { x1, y1, x2, y2, duration };
                    break;

                case 'input':
                    const inputText = document.getElementById('inputText').value;
                    if (!inputText) {
                        setStatus('请输入要输入的文字');
                        return;
                    }
                    action.p = { text: inputText };
                    break;

                case 'wait':
                    const waitDuration = parseInt(document.getElementById('waitDuration').value) || 1000;
                    action.p = { duration: waitDuration };
                    break;

                case 'app_launch':
                    const packageName = document.getElementById('appPackage').value.trim();
                    if (!packageName) {
                        setStatus('请输入应用包名');
                        return;
                    }
                    action.p = { package_name: packageName };
                    break;

                case 'multi_target':
                    const conditions = collectMultiTargetData();
                    const targetIndex = parseInt(document.getElementById('targetIndex').value) || 0;

                    if (conditions.length === 0) {
                        setStatus('请至少添加一个条件');
                        return;
                    }

                    action.p = {
                        conditions: conditions,
                        target_index: targetIndex
                    };
                    break;
            }

            // 添加到当前流程
            if (!scriptData[currentFlow]) {
                scriptData[currentFlow] = [];
            }

            // 创建流程（如果不存在）
            if (scriptData[currentFlow].length === 0) {
                scriptData[currentFlow].push({
                    i: generateId(),
                    n: `${currentFlow === 'sequential' ? '顺序' : currentFlow === 'parallel' ? '并行' : '公共'}流程`,
                    e: true,
                    a: []
                });
            }

            // 添加动作到第一个流程
            scriptData[currentFlow][0].a.push(action);

            // 清空表单
            document.getElementById('actionName').value = '';
            updateActionForm();

            // 更新显示
            renderActionList();
            setStatus(`已添加动作: ${actionName}`);
        }

        // 生成UI指纹
        function generateUIFingerprint(element) {
            return {
                text: element.text || '',
                className: element.className || '',
                resourceId: element.resourceId || '',
                description: element.description || '',
                bounds: element.bounds
            };
        }

        // 生成ID
        function generateId() {
            return 'id_' + Math.random().toString(36).substr(2, 9);
        }

        // 渲染动作列表
        function renderActionList() {
            const container = document.getElementById('actionList');
            container.innerHTML = '';

            const flows = scriptData[currentFlow] || [];

            flows.forEach((flow, flowIndex) => {
                const flowDiv = document.createElement('div');
                flowDiv.innerHTML = `
                    <div style="background: #667eea; color: white; padding: 8px 12px; border-radius: 6px; margin-bottom: 10px; font-weight: 500;">
                        📋 ${flow.n} (${flow.a.length} 个动作)
                    </div>
                `;
                container.appendChild(flowDiv);

                flow.a.forEach((action, actionIndex) => {
                    const actionDiv = document.createElement('div');
                    actionDiv.className = 'action-item';
                    actionDiv.innerHTML = `
                        <div class="action-header">
                            <span class="action-type">${getActionTypeLabel(action.t)}</span>
                            <span class="action-name">${action.n}</span>
                            <div class="action-controls">
                                <button class="btn btn-small btn-warning" onclick="editAction(${flowIndex}, ${actionIndex})">✏️</button>
                                <button class="btn btn-small btn-danger" onclick="deleteAction(${flowIndex}, ${actionIndex})">🗑️</button>
                            </div>
                        </div>
                        <div style="font-size: 12px; color: #666;">
                            ${getActionDescription(action)}
                        </div>
                    `;
                    container.appendChild(actionDiv);
                });
            });
        }

        // 获取动作类型标签
        function getActionTypeLabel(type) {
            const labels = {
                'click': '点击',
                'ocr_click': 'OCR点击',
                'ui_click': 'UI点击',
                'swipe': '滑动',
                'input': '输入',
                'wait': '等待',
                'app_launch': '启动应用',
                'multi_target': '多条件'
            };
            return labels[type] || type;
        }

        // 获取动作描述
        function getActionDescription(action) {
            switch (action.t) {
                case 'click':
                    return `坐标: (${action.p.x}, ${action.p.y})`;
                case 'ocr_click':
                    return `文字: "${action.p.text}"`;
                case 'ui_click':
                    try {
                        const fingerprint = JSON.parse(action.p.selector.fingerprint);

                        // 检查是否是增强指纹
                        if (fingerprint.primary && fingerprint.metadata) {
                            const region = fingerprint.primary.screen_region || 'unknown';
                            const textDisplay = fingerprint.secondary?.text_pattern ||
                                               fingerprint.secondary?.resource_id_pattern ||
                                               fingerprint.primary?.class || '未知元素';
                            const backupInfo = action.p.backup_coords ? ` (备用: ${action.p.backup_coords.x}, ${action.p.backup_coords.y})` : '';
                            return `增强指纹: [${region}] - ${textDisplay}${backupInfo}`;
                        } else {
                            const hashDisplay = fingerprint.hash ? fingerprint.hash.substring(0, 8) : 'unknown';
                            const textDisplay = fingerprint.text || fingerprint.id || fingerprint.class || '未知元素';
                            const backupInfo = action.p.backup_coords ? ` (备用: ${action.p.backup_coords.x}, ${action.p.backup_coords.y})` : '';
                            return `指纹: ${hashDisplay} - ${textDisplay}${backupInfo}`;
                        }
                    } catch (e) {
                        return `UI指纹元素`;
                    }
                case 'swipe':
                    return `从 (${action.p.x1}, ${action.p.y1}) 到 (${action.p.x2}, ${action.p.y2})`;
                case 'input':
                    return `输入: "${action.p.text}"`;
                case 'wait':
                    return `等待 ${action.p.duration}ms`;
                case 'app_launch':
                    return `启动: ${action.p.package_name}`;
                default:
                    return '';
            }
        }

        // 删除动作
        function deleteAction(flowIndex, actionIndex) {
            if (confirm('确定要删除这个动作吗？')) {
                scriptData[currentFlow][flowIndex].a.splice(actionIndex, 1);
                renderActionList();
                setStatus('动作已删除');
            }
        }

        // 测试坐标
        async function testCoordinate() {
            if (!selectedElement || !selectedElement.data) {
                setStatus('❌ 请先选择一个UI元素');
                return;
            }

            const element = selectedElement.data;
            if (!element.bounds) {
                setStatus('❌ 选中的元素没有位置信息');
                return;
            }

            // 计算元素中心点坐标
            const centerX = element.bounds.x + Math.round(element.bounds.width / 2);
            const centerY = element.bounds.y + Math.round(element.bounds.height / 2);

            // 转换为显示坐标（考虑object-fit: contain）
            const img = document.getElementById('deviceScreen');
            if (!img.naturalWidth || !img.naturalHeight) {
                setStatus('❌ 截图未加载');
                return;
            }

            const containerWidth = img.clientWidth;
            const containerHeight = img.clientHeight;
            const naturalWidth = img.naturalWidth;
            const naturalHeight = img.naturalHeight;
            const imageAspect = naturalWidth / naturalHeight;
            const containerAspect = containerWidth / containerHeight;

            let actualWidth, actualHeight, offsetX, offsetY;

            if (imageAspect > containerAspect) {
                // 图片更宽，以宽度为准
                actualWidth = containerWidth;
                actualHeight = containerWidth / imageAspect;
                offsetX = 0;
                offsetY = (containerHeight - actualHeight) / 2;
            } else {
                // 图片更高，以高度为准
                actualHeight = containerHeight;
                actualWidth = containerHeight * imageAspect;
                offsetX = (containerWidth - actualWidth) / 2;
                offsetY = 0;
            }

            const scaleX = actualWidth / naturalWidth;
            const scaleY = actualHeight / naturalHeight;
            const displayX = centerX * scaleX + offsetX;
            const displayY = centerY * scaleY + offsetY;

            // 显示测试标记
            showTestMarker(displayX, displayY, element);

            // 显示详细信息
            setStatus(`🎯 测试坐标: (${centerX}, ${centerY}) - ${element.text || element.className || '未知元素'}`);

            // 在调试日志中显示详细信息
            addDebugLog('=== 测试坐标结果 ===');
            addDebugLog(`🎯 元素中心坐标: (${centerX}, ${centerY})`);
            addDebugLog(`📱 元素类型: ${element.className || '未知'}`);
            if (element.text) addDebugLog(`📝 文本内容: ${element.text}`);
            if (element.resourceId) addDebugLog(`🆔 资源ID: ${element.resourceId}`);
            addDebugLog(`📍 元素位置: (${element.bounds.x}, ${element.bounds.y})`);
            addDebugLog(`📏 元素尺寸: ${element.bounds.width} x ${element.bounds.height}`);
            addDebugLog(`🖱️ 可点击: ${element.clickable ? '是' : '否'}`);
            addDebugLog('==================');
        }

        // 保存脚本
        function saveScript() {
            const scriptName = document.getElementById('scriptName').value.trim();
            const scriptDesc = document.getElementById('scriptDesc').value.trim();
            const targetApp = document.getElementById('targetApp').value.trim();

            if (!scriptName) {
                setStatus('请输入脚本名称');
                return;
            }

            const script = {
                n: scriptName,
                d: scriptDesc,
                a: targetApp ? [targetApp] : [],
                s: scriptData.sequential || [],
                p: scriptData.parallel || [],
                g: scriptData.global || []
            };

            // 下载JSON文件
            const blob = new Blob([JSON.stringify(script, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${scriptName}.json`;
            a.click();
            URL.revokeObjectURL(url);

            setStatus('脚本已保存');
        }

        // 预览脚本
        function previewScript() {
            const scriptName = document.getElementById('scriptName').value.trim() || '未命名脚本';
            const scriptDesc = document.getElementById('scriptDesc').value.trim();
            const targetApp = document.getElementById('targetApp').value.trim();

            const script = {
                n: scriptName,
                d: scriptDesc,
                a: targetApp ? [targetApp] : [],
                s: scriptData.sequential || [],
                p: scriptData.parallel || [],
                g: scriptData.global || []
            };

            const jsonStr = JSON.stringify(script, null, 2);

            // 创建预览窗口
            const previewWindow = window.open('', '_blank', 'width=800,height=600');
            previewWindow.document.write(`
                <html>
                <head>
                    <title>脚本预览 - ${scriptName}</title>
                    <style>
                        body { font-family: monospace; padding: 20px; background: #f5f5f5; }
                        pre { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
                        .header { background: #667eea; color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>📋 脚本预览: ${scriptName}</h2>
                        <p>描述: ${scriptDesc || '无描述'}</p>
                        <p>目标应用: ${targetApp || '无指定'}</p>
                    </div>
                    <pre>${jsonStr}</pre>
                </body>
                </html>
            `);
        }

        // 清空脚本
        function clearScript() {
            if (confirm('确定要清空所有脚本内容吗？')) {
                scriptData = { sequential: [], parallel: [], global: [] };
                selectedElement = null;
                document.getElementById('scriptName').value = '';
                document.getElementById('scriptDesc').value = '';
                document.getElementById('targetApp').value = '';
                renderActionList();
                updateSelectedElement();
                setStatus('脚本已清空');
            }
        }

        // 运行脚本
        async function runScript() {
            if (!API_BASE) {
                setStatus('请先配置服务器地址');
                showServerConfig();
                return;
            }

            const scriptName = document.getElementById('scriptName').value.trim() || '临时脚本';
            const scriptDesc = document.getElementById('scriptDesc').value.trim();
            const targetApp = document.getElementById('targetApp').value.trim();

            const script = {
                n: scriptName,
                d: scriptDesc,
                a: targetApp ? [targetApp] : [],
                s: scriptData.sequential || [],
                p: scriptData.parallel || [],
                g: scriptData.global || []
            };

            setStatus('正在启动脚本...', true);

            try {
                const response = await fetch(API_BASE + '/script/start-json', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(script)
                });

                const result = await response.json();
                if (result.success) {
                    setStatus(`脚本启动成功: ${scriptName}`);
                } else {
                    setStatus('脚本启动失败: ' + result.error);
                }
            } catch (error) {
                setStatus('脚本启动失败: ' + error.message);
            }
        }

        // 停止脚本
        async function stopScript() {
            if (!API_BASE) {
                setStatus('请先配置服务器地址');
                showServerConfig();
                return;
            }

            setStatus('正在停止脚本...', true);

            try {
                const response = await fetch(API_BASE + '/script/stop', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });

                const result = await response.json();
                if (result.success) {
                    setStatus('脚本已停止');
                } else {
                    setStatus('停止脚本失败: ' + result.error);
                }
            } catch (error) {
                setStatus('停止脚本失败: ' + error.message);
            }
        }

        // 多条件匹配相关函数
        let multiTargetCounter = 0;

        // 添加多条件匹配条件
        function addMultiTarget() {
            const container = document.getElementById('multiTargets');
            const conditionId = 'condition_' + (++multiTargetCounter);

            const conditionHTML = `
                <div class="multi-condition" id="${conditionId}" style="border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin: 10px 0; background: #f9f9f9;">
                    <div style="display: flex; justify-content: between; align-items: center; margin-bottom: 10px;">
                        <strong>条件 ${multiTargetCounter}</strong>
                        <button type="button" class="btn btn-small btn-danger" onclick="removeMultiTarget('${conditionId}')" style="margin-left: auto;">🗑️ 删除</button>
                    </div>

                    <div class="form-group">
                        <label>条件类型:</label>
                        <select class="form-control" id="${conditionId}_type" onchange="updateMultiTargetForm('${conditionId}')">
                            <option value="ocr">OCR文字条件</option>
                            <option value="ui">UI节点条件</option>
                        </select>
                    </div>

                    <div id="${conditionId}_form">
                        <!-- 动态生成的表单内容 -->
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', conditionHTML);
            updateMultiTargetForm(conditionId);
        }

        // 删除多条件匹配条件
        function removeMultiTarget(conditionId) {
            const element = document.getElementById(conditionId);
            if (element) {
                element.remove();
            }
        }

        // 更新多条件匹配表单
        function updateMultiTargetForm(conditionId) {
            const type = document.getElementById(conditionId + '_type').value;
            const formContainer = document.getElementById(conditionId + '_form');

            let formHTML = '';

            if (type === 'ocr') {
                formHTML = `
                    <div class="form-group">
                        <label>OCR文字:</label>
                        <input type="text" class="form-control" id="${conditionId}_text" placeholder="要检测的文字">
                    </div>
                    <div class="form-group">
                        <label>匹配模式:</label>
                        <select class="form-control" id="${conditionId}_mode">
                            <option value="contains">包含</option>
                            <option value="exact">完全匹配</option>
                            <option value="regex">正则表达式</option>
                        </select>
                    </div>
                `;
            } else if (type === 'ui') {
                formHTML = `
                    <div class="form-group">
                        <label>UI节点文字:</label>
                        <input type="text" class="form-control" id="${conditionId}_text" placeholder="节点文字">
                    </div>
                    <div class="form-group">
                        <label>资源ID:</label>
                        <input type="text" class="form-control" id="${conditionId}_resourceId" placeholder="resource-id">
                    </div>
                    <div class="form-group">
                        <label>类名:</label>
                        <input type="text" class="form-control" id="${conditionId}_className" placeholder="class">
                    </div>
                `;
            }

            formContainer.innerHTML = formHTML;
        }

        // 收集多条件匹配数据
        function collectMultiTargetData() {
            const conditions = [];
            const container = document.getElementById('multiTargets');
            const conditionElements = container.querySelectorAll('.multi-condition');

            conditionElements.forEach(element => {
                const conditionId = element.id;
                const type = document.getElementById(conditionId + '_type').value;
                const text = document.getElementById(conditionId + '_text').value.trim();

                if (!text) return; // 跳过空条件

                const condition = {
                    type: type,
                    text: text
                };

                if (type === 'ocr') {
                    condition.mode = document.getElementById(conditionId + '_mode').value;
                } else if (type === 'ui') {
                    const resourceId = document.getElementById(conditionId + '_resourceId').value.trim();
                    const className = document.getElementById(conditionId + '_className').value.trim();

                    if (resourceId) condition.resourceId = resourceId;
                    if (className) condition.className = className;
                }

                conditions.push(condition);
            });

            return conditions;
        }



        // 显示测试坐标标记（模仿Python脚本的地图标记样式）
        function showTestMarker(displayX, displayY, element) {
            clearTestMarker();

            const overlay = document.getElementById('overlayElements');

            // 创建地图标记样式的标记
            testMarker = document.createElement('div');
            testMarker.style.position = 'absolute';
            testMarker.style.left = (displayX - 15) + 'px';
            testMarker.style.top = (displayY - 30) + 'px';
            testMarker.style.width = '30px';
            testMarker.style.height = '30px';
            testMarker.style.backgroundColor = element ? 'rgba(0, 200, 0, 0.9)' : 'rgba(255, 71, 87, 0.9)';
            testMarker.style.border = '3px solid white';
            testMarker.style.borderRadius = '50% 50% 50% 0';
            testMarker.style.transform = 'rotate(-45deg)';
            testMarker.style.pointerEvents = 'none';
            testMarker.style.zIndex = '1000';
            testMarker.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';

            // 添加中心点
            const centerDot = document.createElement('div');
            centerDot.style.position = 'absolute';
            centerDot.style.left = '50%';
            centerDot.style.top = '50%';
            centerDot.style.width = '8px';
            centerDot.style.height = '8px';
            centerDot.style.backgroundColor = 'white';
            centerDot.style.borderRadius = '50%';
            centerDot.style.transform = 'translate(-50%, -50%) rotate(45deg)';
            testMarker.appendChild(centerDot);

            overlay.appendChild(testMarker);
        }

        // 清除测试标记
        function clearTestMarker() {
            if (testMarker) {
                testMarker.remove();
                testMarker = null;
            }
        }

        // 显示元素详细信息
        function showElementDetails(element, x, y) {
            // 在调试日志区域显示详细信息
            const debugLog = document.getElementById('debugLog');
            if (!debugLog) return;

            let details;
            if (element) {
                details = [
                    `🎯 测试坐标: (${x}, ${y})`,
                    `✅ 找到UI元素`,
                    `📱 元素类型: ${element.className || '未知'}`,
                    element.text ? `📝 文本内容: ${element.text}` : '',
                    element.resourceId ? `🆔 资源ID: ${element.resourceId}` : '',
                    element.contentDesc ? `📄 内容描述: ${element.contentDesc}` : '',
                    `📍 元素位置: (${element.bounds.x}, ${element.bounds.y})`,
                    `📏 元素尺寸: ${element.bounds.width} x ${element.bounds.height}`,
                    `🖱️ 可点击: ${element.clickable ? '是' : '否'}`,
                    `👁️ 可见: ${element.visible ? '是' : '否'}`,
                    `🔧 可编辑: ${element.editable ? '是' : '否'}`
                ].filter(Boolean).join('\n');
            } else {
                details = [
                    `🎯 测试坐标: (${x}, ${y})`,
                    `❌ 未找到UI元素`,
                    `💡 提示: 该位置可能是背景或空白区域`
                ].join('\n');
            }

            // 添加到调试日志
            addDebugLog('=== 坐标测试结果 ===');
            addDebugLog(details);
            addDebugLog('==================');
        }
    </script>
</body>
</html>
